<!-- Delete Confirmation Modal -->
<c-modal
  [visible]="showDeleteModal"
  (visibleChange)="onDeleteModalChange($event)"
  [backdrop]="true"
  [size]="'sm'"
>
  <c-modal-header>
    <h5 cModalTitle>{{ 'disease.delete' | translate | async }}</h5>
    <button
      cButtonClose
      (click)="onDeleteModalChange(false)"
    ></button>
  </c-modal-header>
  <c-modal-body>
    <p>{{ 'disease.confirmDelete' | translate | async }}</p>
  </c-modal-body>
  <c-modal-footer>
    <app-action-button
      color="secondary"
      icon="cilX"
      text="common.cancel"
      shape="rounded-pill"
      (clicked)="onDeleteModalChange(false)"
    ></app-action-button>

    <app-action-button
      color="danger"
      icon="cilTrash"
      text="common.delete"
      shape="rounded-pill"
      (clicked)="confirmDelete()"
    ></app-action-button>
  </c-modal-footer>
</c-modal>

<!-- Main Content -->
@if(!showForm) {
  <c-card>
    <c-card-header class="custom-card">
      <div class="card-header bg-info d-flex justify-content-between align-items-center">
        <h4 class="mb-0 text-white">
          <svg cIcon name="cilMedicalCross" class="me-2" width="24" height="24"></svg>
          {{ 'disease.title' | translate | async }}
        </h4>
        <div class="d-flex gap-2">
          <app-action-button
            color="light"
            icon="cilPlus"
            text="disease.create"
            shape="rounded-pill"
            (clicked)="onCreateDisease()"
          ></app-action-button>
        </div>
      </div>
    </c-card-header>

    <c-card-body>
      <!-- Loading State -->
      @if(loading) {
        <div class="text-center py-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">{{ 'common.loading' | translate | async }}</span>
          </div>
          <p class="mt-3">{{ 'common.loading' | translate | async }}</p>
        </div>
      }

      <!-- Error State -->
      @if(error) {
        <c-alert color="danger" [dismissible]="true" (visibleChange)="dismissError()">
          <svg cIcon name="cilWarning" class="me-2" width="24" height="24"></svg>
          {{ error }}
        </c-alert>
      }

      <!-- No Results State -->
      @if(!loading && diseases.length === 0) {
        <div class="text-center py-5">
          <svg cIcon name="cilMedicalCross" class="text-muted mb-3" width="48" height="48"></svg>
          <h5>{{ 'disease.noResults' | translate | async }}</h5>
          <p class="text-muted">{{ 'disease.tryDifferentSearch' | translate | async }}</p>
        </div>
      }

      <!-- Diseases Table -->
      @if(!loading && diseases.length > 0) {
        <table cTable responsive hover striped>
          <thead>
            <tr>
              <th class="text-center" style="width: 5%;">#</th>
              <th class="text-start" style="width: 30%;">{{ 'disease.nameEn' | translate | async }}</th>
              <th class="text-end" style="width: 30%;">{{ 'disease.nameAr' | translate | async }}</th>
              <th class="text-center" style="width: 20%;">{{ 'common.actions' | translate | async }}</th>
            </tr>
          </thead>
          <tbody>
            @for(disease of diseases; track disease.id; let i = $index) {
              <tr>
                <td class="text-center">{{ (currentPage - 1) * pageSize + i + 1 }}</td>
                <td class="text-start">{{ disease.nameEn }}</td>
                <td class="text-end">{{ disease.nameAr }}</td>
                <td class="text-center">
                  <div class="d-flex gap-2 justify-content-center">
                    <app-action-button
                      color="primary"
                      icon="cilPencil"
                      text="common.edit"
                      shape="rounded-pill"
                      size="sm"
                      (clicked)="onEditDisease(disease)"
                    ></app-action-button>

                    <app-action-button
                      color="danger"
                      icon="cilTrash"
                      text="common.delete"
                      shape="rounded-pill"
                      size="sm"
                      (clicked)="onDeleteDisease(disease.id!)"
                    ></app-action-button>
                  </div>
                </td>
              </tr>
            }
          </tbody>
        </table>

        <!-- Standardized Pagination -->
        <app-pagination
          [currentPage]="currentPage"
          [totalItems]="totalItems"
          [pageSize]="pageSize"
          [layout]="'simple'"
          [showInfo]="true"
          [size]="'sm'"
          (pageChange)="onPageChange($event)"
        ></app-pagination>
      }
    </c-card-body>
  </c-card>
}

<!-- Disease Form -->
@if(showForm) {
  <c-card>
    <c-card-header>
      <h4 class="mb-0">
        {{ (isEditMode ? 'disease.edit' : 'disease.create') | translate | async }}
      </h4>
    </c-card-header>
    <c-card-body>
      <form [formGroup]="diseaseForm" (ngSubmit)="onFormSubmit()">
        <div class="mb-3">
          <label for="nameEn" class="form-label">{{ 'disease.nameEn' | translate | async }}</label>
          <input
            type="text"
            class="form-control"
            id="nameEn"
            formControlName="nameEn"
            [placeholder]="'disease.nameEnPlaceholder' | translate | async"
            required
          >
        </div>

        <div class="mb-3">
          <label for="nameAr" class="form-label">{{ 'disease.nameAr' | translate | async }}</label>
          <input
            type="text"
            class="form-control arabic-input"
            id="nameAr"
            formControlName="nameAr"
            [placeholder]="'disease.nameArPlaceholder' | translate | async"
            dir="rtl"
            lang="ar"
            required
          >
        </div>

        <div class="mt-3 d-flex gap-2">
          <app-action-button
            color="primary"
            type="submit"
            [icon]="isEditMode ? 'cilPencil' : 'cilPlus'"
            [text]="isEditMode ? 'disease.update' : 'disease.create'"
            shape="rounded-pill"
          ></app-action-button>

          <app-action-button
            color="secondary"
            icon="cilX"
            text="common.cancel"
            shape="rounded-pill"
            (clicked)="onFormCancel()"
          ></app-action-button>
        </div>
      </form>
    </c-card-body>
  </c-card>
}
