import { Icon } from 'leaflet';

// Fix for Leaflet icon issue
export function fixLeafletIcons(): void {
  delete (Icon.Default.prototype as any)._getIconUrl;

  Icon.Default.mergeOptions({
    iconRetinaUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png',
    iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
    shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png'
  });
}
