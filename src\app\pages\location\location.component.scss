.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  margin-bottom: 1rem;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.list-group-item {
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }
}

.form-control, .form-select {
  &:focus {
    border-color: #321fdb;
    box-shadow: 0 0 0 0.2rem rgba(50, 31, 219, 0.25);
  }
}

.invalid-feedback {
  display: block;
}

button[cButton] {
  i {
    margin-right: 0.5rem;
  }
}

.gap-2 {
  gap: 0.5rem;
}

:host {
  display: block;
}

// Selected item styling
.selected-item {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 70%;
    background-color: var(--cui-primary);
    border-radius: 4px;
  }

  .icon-container {
    background-color: rgba(var(--cui-primary-rgb), 0.2);
  }
}

// Icon styling
.icon-container {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(var(--cui-primary-rgb), 0.1);
  color: var(--cui-primary);
  transition: all 0.2s ease;

  &.info {
    background-color: rgba(var(--cui-info-rgb), 0.1);
    color: var(--cui-info);
  }
}

// Empty state styling
.empty-state {
  i {
    color: var(--cui-gray-400);
  }

  h5 {
    color: var(--cui-gray-700);
    font-weight: 600;
  }

  p {
    color: var(--cui-gray-600);
  }
}

// Form animations and styling
.animated {
  animation-duration: 0.4s;
  animation-fill-mode: both;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fadeIn {
  animation-name: fadeIn;
}

// Custom card styling
.custom-card {
  overflow: hidden;
  transition: all 0.3s ease;

  .card-header {
    border-bottom: 0;

    &.bg-primary {
      background: var(--cui-primary);
    }

    &.bg-info {
      background: var(--cui-info);
    }
  }
}

// Shadow hover effect
.shadow-hover {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1) !important;
  }
}

// Button hover effect
.btn-hover-effect {
  transition: all 0.25s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
  }
}

// Modal styling
.modal-icon-container {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Add extra padding for form inputs
.form-control, .form-select {
  padding: 0.625rem 1rem;

  &:focus {
    box-shadow: 0 0 0 0.25rem rgba(var(--cui-primary-rgb), 0.25);
    border-color: rgba(var(--cui-primary-rgb), 0.4);
  }
}

// RTL support
html[dir="rtl"] {
  .selected-item {
    &::before {
      left: auto;
      right: -15px;
    }
  }

  .btn i {
    margin-left: 0.375rem;
    margin-right: 0;
  }
}
