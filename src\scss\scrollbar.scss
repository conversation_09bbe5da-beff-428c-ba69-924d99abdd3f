/* Custom Scrollbar Styles */

::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: rgba(var(--cui-primary-rgb), 0.5);
  border-radius: 10px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--cui-primary-rgb), 0.7);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(var(--cui-primary-rgb), 0.5) #f1f1f1;
}

// Custom class for elements that need a custom scrollbar
.custom-scrollbar {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(var(--cui-primary-rgb), 0.5);
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(var(--cui-primary-rgb), 0.7);
  }

  // Firefox
  scrollbar-width: thin;
  scrollbar-color: rgba(var(--cui-primary-rgb), 0.5) #f1f1f1;
}
