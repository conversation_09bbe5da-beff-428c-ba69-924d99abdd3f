<!-- Delete Confirmation Modal -->
<c-modal
  [visible]="showDeleteModal"
  (visibleChange)="onDeleteModalChange($event)"
  [backdrop]="true"
  [size]="'sm'"
>
  <c-modal-header>
    <h5 cModalTitle>{{ 'admin.delete' | translate | async }}</h5>
    <button
      cButtonClose
      (click)="onDeleteModalChange(false)"
    ></button>
  </c-modal-header>
  <c-modal-body>
    <p>{{ 'admin.confirmDelete' | translate | async }}</p>
  </c-modal-body>
  <c-modal-footer>
    <app-action-button
      color="secondary"
      text="common.cancel"
      icon="cilX"
      (clicked)="onDeleteModalChange(false)"
    ></app-action-button>

    <app-action-button
      color="danger"
      text="common.delete"
      icon="cilTrash"
      (clicked)="confirmDelete()"
    ></app-action-button>
  </c-modal-footer>
</c-modal>

<!-- Error Alert -->
<c-alert
  *ngIf="error$ | async as error"
  color="danger"
  [dismissible]="true"
  (visible)="dismissError()"
  class="mb-3"
>
  {{ error }}
</c-alert>

@if(!showForm){
<!-- Main Content using DataTableComponent -->
<app-data-table
  [title]="'admin.title'"
  [items]="(admins$ | async) || []"
  [loading]="(loading$ | async) || false"
  [error]="(error$ | async)"
  [columns]="tableColumns"
  [searchForm]="searchForm"
  [currentPage]="currentPage"
  [pageSize]="pageSize"
  [totalItems]="totalItems"
  [createButtonText]="'admin.create'"
  [searchPlaceholder]="'admin.searchPlaceholder'"
  (search)="onSearch()"
  (pageChange)="onPageChange($event)"
  (create)="onCreateAdmin()"
  (pageSizeChange)="onPageSizeChange($event)"
  (dismissErrorEvent)="dismissError()"
>
  <!-- Custom Item Template -->
  <ng-template #itemTemplate let-admin>
    <td>
      <div class="d-flex align-items-center">
        <img
          [src]="admin.imageUrl || defaultAvatarPath"
          [alt]="admin.firstName"
          class="avatar"
          [ngClass]="{'default-avatar': !admin.imageUrl}"
          (error)="handleImageError($event)"
        >
        <div class="ms-3">
          <div class="fw-semibold">{{ admin.firstName }}</div>
        </div>
      </div>
    </td>
    <td>{{ admin.lastName }}</td>
    <td>{{ admin.phoneNumber }}</td>
    <td>{{ admin.email }}</td>
  </ng-template>

  <!-- Custom Actions Template -->
  <ng-template #actionsTemplate let-admin>
    <app-action-button
      [text]="'common.edit'"
      [icon]="'cilPencil'"
      [color]="'primary'"
      [shape]="'rounded-pill'"
      [size]="'sm'"
      [tooltip]="'admin.editTooltip'"
      (clicked)="onEditAdmin(admin)"
      class="me-2"
    ></app-action-button>

    <app-action-button
      [text]="'common.delete'"
      [icon]="'cilTrash'"
      [color]="'danger'"
      [shape]="'rounded-pill'"
      [size]="'sm'"
      [tooltip]="'admin.deleteTooltip'"
      (clicked)="onDeleteAdmin(admin.id)"
    ></app-action-button>
  </ng-template>
</app-data-table>
}

<!-- Admin Form Modal -->
<app-admin-form
  *ngIf="showForm"
  [admin]="selectedAdmin"
  (submit)="onFormSubmit($event)"
  (cancel)="onFormCancel()"
></app-admin-form>
