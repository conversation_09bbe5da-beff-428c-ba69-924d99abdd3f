.app-version-page {
  // Filter Card
  .filter-card {
    border: 1px solid var(--cui-border-color);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    .filter-header {
      background: rgba(var(--cui-primary-rgb), 0.05);
      border-bottom: 1px solid var(--cui-border-color);
      padding: 16px 20px;
      border-radius: 8px 8px 0 0;

      .filter-icon-wrapper {
        width: 40px;
        height: 40px;
        background: var(--cui-primary);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
      }

      .filter-title {
        font-weight: 600;
        font-size: 16px;
      }

      .filter-subtitle {
        color: #6c757d;
        font-size: 13px;
        font-weight: 500;
      }

      .clear-all-btn {
        border-radius: 6px;
      }
    }

    .filter-body {
      padding: 20px;
    }
  }

  // Filter Groups
  .filter-group {
    .filter-label {
      font-weight: 600;
      margin-bottom: 8px;
      font-size: 14px;
      display: flex;
      align-items: center;
      color: #212529;

      i {
        color: var(--cui-primary);
        margin-right: 8px;
      }
    }

    .input-group {
      border-radius: 6px;
      overflow: hidden;

      .input-group-text {
        background: rgba(var(--cui-primary-rgb), 0.1);
        border: 1px solid var(--cui-border-color);
        border-right: none;
        color: var(--cui-primary);
      }

      .filter-input {
        border: 1px solid var(--cui-border-color);
        border-left: none;

        &:focus {
          border-color: var(--cui-primary);
          box-shadow: 0 0 0 2px rgba(var(--cui-primary-rgb), 0.2);
        }
      }

      .btn-clear-field {
        border: 1px solid var(--cui-border-color);
        border-left: none;
        background: var(--cui-light);

        &:hover {
          background: var(--cui-danger);
          color: white;
        }
      }
    }

    .select-wrapper {
      position: relative;

      .filter-select {
        border-radius: 6px;
        border: 1px solid var(--cui-border-color);
        padding-right: 35px;
        appearance: none;

        &:focus {
          border-color: var(--cui-primary);
          box-shadow: 0 0 0 2px rgba(var(--cui-primary-rgb), 0.2);
        }
      }

      .select-arrow {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #495057;
        pointer-events: none;
        font-weight: 600;
      }
    }
  }

  // Filter Actions
  .filter-actions {
    .search-btn {
      background: var(--cui-primary);
      border: none;
      border-radius: 6px;
      font-weight: 600;
    }

    .clear-btn {
      border-radius: 6px;

      &:hover {
        background: var(--cui-danger);
        color: white;
        border-color: var(--cui-danger);
      }
    }
  }

  // Filter Summary
  .filter-summary {
    background: rgba(var(--cui-info-rgb), 0.05);
    border: 1px solid rgba(var(--cui-info-rgb), 0.2);
    border-radius: 6px;
    padding: 10px 12px;

    .filter-summary-label {
      font-weight: 700;
      color: #0f5132;
      font-size: 13px;
    }

    .filter-badge {
      position: relative;
      padding-right: 28px;
      font-size: 11px;
      border-radius: 12px;

      .btn-close-badge {
        position: absolute;
        right: 3px;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255,255,255,0.3);
        border: none;
        border-radius: 50%;
        width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;

        &:hover {
          background: rgba(255,255,255,0.5);
        }
      }
    }
  }

  // Table
  .table-responsive {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    .table {
      margin-bottom: 0;

      th {
        font-weight: 600;
        font-size: 13px;
        background: var(--cui-dark);
        color: white;
        padding: 12px 10px;
        border-top: none;
      }

      td {
        vertical-align: middle;
        padding: 10px;
        color: #212529;
        font-weight: 500;

        strong {
          font-weight: 700;
        }
      }

      .btn {
        border-radius: 4px;
        font-size: 12px;
        padding: 4px 8px;
      }
    }
  }

  .alert {
    border: none;
    border-radius: 8px;
  }

  .card {
    border: 1px solid var(--cui-border-color);

    .card-header {
      background-color: var(--cui-body-bg);
      border-bottom: 1px solid var(--cui-border-color);
    }
  }

  .btn {
    border-radius: 4px;
    font-weight: 600;
  }

  .form-control, .form-select {
    border-radius: 4px;
    border: 1px solid var(--cui-border-color);

    &:focus {
      border-color: var(--cui-primary);
      box-shadow: 0 0 0 2px rgba(var(--cui-primary-rgb), 0.2);
    }

    &.is-invalid {
      border-color: var(--cui-danger);
      box-shadow: 0 0 0 2px rgba(var(--cui-danger-rgb), 0.2);
    }
  }

  .invalid-feedback {
    display: block;
    font-size: 13px;
    color: var(--cui-danger);
  }

  .form-text {
    font-size: 13px;
    color: #6c757d;
    font-weight: 500;
  }

  .badge {
    font-size: 11px;
    padding: 3px 6px;
    border-radius: 4px;
  }
}

.modal {
  .modal-header {
    border-bottom: 1px solid var(--cui-border-color);

    .modal-title {
      font-weight: 600;
    }
  }

  .modal-body {
    padding: 20px;

    .alert {
      margin-bottom: 12px;
      padding: 10px 12px;
      border-radius: 6px;
    }

    .form-group, .mb-3 {
      margin-bottom: 12px;
    }

    label {
      font-weight: 600;
      margin-bottom: 6px;
      color: #212529;
    }
  }

  .modal-footer {
    border-top: 1px solid var(--cui-border-color);
    padding: 12px 20px;

    .btn {
      min-width: 70px;

      .spinner-border-sm {
        width: 12px;
        height: 12px;
      }
    }
  }
}

@media (max-width: 768px) {
  .app-version-page {
    .d-flex.justify-content-between{flex-direction:column;gap:12px}
    .filter-card{margin:0 -6px 12px;.filter-header{padding:12px}.filter-body{padding:12px;.row.g-4{gap:12px;.col-lg-4,.col-md-6,.col-md-12{flex:0 0 100%;max-width:100%}}.filter-actions .d-flex.gap-2{flex-direction:column;.search-btn,.clear-btn{width:100%}}}}
    .table-responsive{font-size:12px;margin:0 -6px;.table{th,td{font-size:11px;padding:8px 6px}}.d-flex.gap-1{flex-direction:column;gap:2px;.btn{font-size:10px;padding:2px 6px}}}
  }
}

[data-theme="dark"] {
  .app-version-page {
    .filter-card {
      background-color: var(--cui-dark);
      border-color: var(--cui-border-color-dark);

      .filter-header {
        background-color: var(--cui-dark);
        border-bottom-color: var(--cui-border-color-dark);

        .filter-subtitle {
          color: #adb5bd;
        }
      }
    }

    .filter-label {
      color: #f8f9fa !important;
    }

    .select-arrow {
      color: #adb5bd !important;
    }

    .form-text {
      color: #adb5bd !important;
    }

    .filter-summary-label {
      color: #20c997 !important;
    }

    .table {
      --cui-table-bg: var(--cui-dark);
      color: var(--cui-light);

      td {
        color: #f8f9fa;
        font-weight: 500;
      }
    }

    .form-control, .form-select {
      background-color: var(--cui-dark);
      border-color: var(--cui-border-color-dark);
      color: #f8f9fa;
      font-weight: 500;
    }
  }

  .modal {
    .modal-content {
      background-color: var(--cui-dark);
      border-color: var(--cui-border-color-dark);
    }

    label {
      color: #f8f9fa !important;
    }

    .form-text {
      color: #adb5bd !important;
    }

    &.fw-semibold {
      color: #f8f9fa !important;
    }
  }
}

// Force Update Modal Enhancements
.force-update-select {
  border-radius: 0 8px 8px 0 !important;
  border-left: none !important;
  transition: all 0.3s ease;
  font-weight: 500;

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
    border-color: #ffc107 !important;
  }

  option {
    padding: 8px 12px;

    &:hover {
      background-color: #fff3cd;
    }

    &.text-primary {
      background-color: #e3f2fd;
      font-weight: 600;
    }
  }
}

.input-group-text {
  border-radius: 8px 0 0 8px !important;
  border-right: none !important;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;

  i {
    font-size: 1.1rem;
  }
}

.alert-sm {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 6px;

  i {
    font-size: 1rem;
  }
}

// Enhanced labels
label.fw-semibold {
  color: #212529;
  font-size: 0.95rem;
  margin-bottom: 0.5rem;
  font-weight: 700;

  i {
    font-size: 1.1rem;
    color: var(--cui-primary);
  }
}



.enhanced-input,.enhanced-select{border-radius:0 8px 8px 0!important;border-left:none!important;font-weight:500;&:focus{box-shadow:0 0 0 .2rem rgba(13,110,253,.25)!important;border-color:#0d6efd!important}&.is-invalid{border-color:#dc3545!important;box-shadow:0 0 0 .2rem rgba(220,53,69,.25)!important}}
.enhanced-select option{padding:8px 12px}
c-modal{
  .modal-header.bg-warning-subtle{background:#fff3cd!important;border-bottom:2px solid #ffc107;.text-warning-emphasis{color:#856404!important;font-weight:600}}
  .alert-danger.border-danger{border-left:4px solid #dc3545!important;.fs-1{font-size:3rem!important}.alert-heading{color:#721c24;font-weight:600}}
  .card.border-warning{border-color:#ffc107!important;.card-header.bg-warning-subtle{background:#fff3cd!important;.card-title{color:#856404;font-weight:600}}}
  .form-check.mt-4{border:2px solid #dee2e6;&:hover{border-color:#0d6efd}.form-check-input{&:checked{background-color:#198754;border-color:#198754}}.form-check-label{color:#495057;cursor:pointer;&.fw-semibold{font-weight:600!important}}}
  .modal-footer.bg-light{background:#f8f9fa!important;border-top:2px solid #dee2e6;.btn-danger{background:#dc3545;border-color:#dc3545;font-weight:600;&:hover{background:#c82333;&:disabled{opacity:.65}}}}
}
