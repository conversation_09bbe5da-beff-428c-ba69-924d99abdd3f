/* Leaflet Map Custom Styles */
/* Make sure Leaflet container takes the full dimensions of its parent */
.leaflet-container {
  width: 100% !important;
  height: 100% !important;
  z-index: 1;
}

/* Fix marker z-index */
.leaflet-marker-pane {
  z-index: 600 !important;
}

.leaflet-tooltip-pane {
  z-index: 650 !important;
}

.leaflet-popup-pane {
  z-index: 700 !important;
}

/* Fix Leaflet image paths */
.leaflet-default-icon-path {
  background-image: url("/assets/leaflet/marker-icon.png") !important;
}

.leaflet-control-layers-toggle {
  background-image: url("/assets/leaflet/layers.png") !important;
  width: 36px !important;
  height: 36px !important;
}

.leaflet-retina .leaflet-control-layers-toggle {
  background-image: url("/assets/leaflet/layers-2x.png") !important;
  background-size: 26px 26px !important;
}

.leaflet-marker-shadow {
  background-image: url("/assets/leaflet/marker-shadow.png") !important;
}

/* Fix marker icons */
.leaflet-marker-icon,
.leaflet-marker-shadow {
  max-width: none !important;
  max-height: none !important;
}

/* Style for tooltips */
.leaflet-tooltip {
  background: white !important;
  border: none !important;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2) !important;
  padding: 5px 10px !important;
  border-radius: 4px !important;
  font-weight: 600 !important;
  white-space: nowrap !important;
}

/* Style for the distance tooltip */
.distance-tooltip {
  background: white !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  padding: 5px 12px !important;
  border-radius: 20px !important;
  font-weight: 600 !important;
  color: #0c4a6e !important;
  white-space: nowrap !important;
  font-size: 12px !important;
  z-index: 1000 !important;
}

.distance-tooltip::before {
  display: none !important;
}

/* Leaflet Controls */
.leaflet-control-container .leaflet-control {
  z-index: 800 !important;
}

.leaflet-control-zoom {
  border: none !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
}

.leaflet-control-zoom a {
  color: #0c4a6e !important;
  width: 30px !important;
  height: 30px !important;
  line-height: 30px !important;
}

/* Popup styling */
.leaflet-popup-content-wrapper {
  border-radius: 8px !important;
  box-shadow: 0 3px 14px rgba(0, 0, 0, 0.2) !important;
}

.leaflet-popup-content {
  margin: 12px 16px !important;
  font-size: 13px !important;
}

.leaflet-popup-tip {
  box-shadow: 0 3px 14px rgba(0, 0, 0, 0.2) !important;
}

/* Fix map attribution control */
.leaflet-control-attribution {
  background-color: rgba(255, 255, 255, 0.8) !important;
  padding: 2px 8px !important;
  font-size: 10px !important;
}
