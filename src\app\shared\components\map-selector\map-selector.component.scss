.map-container {
  position: relative;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  display: block;
  height: 400px;
}

.map {
  height: 400px;
  width: 100%;
  z-index: 1;
  border-radius: 8px;
  display: block;
  position: absolute;
}

.map-info {
  background-color: rgba(255, 255, 255, 0.8);
  padding: 8px 12px;
  border-radius: 4px;
  position: absolute;
  bottom: 10px;
  left: 10px;
  z-index: 2;
  max-width: 300px;
  font-size: 0.9rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// Improvements for map controls
:host ::ng-deep {
  .leaflet-control-zoom {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

    a {
      border-radius: 0 !important;
    }
  }

  .leaflet-control-attribution {
    font-size: 0.7rem;
    background-color: rgba(255, 255, 255, 0.7);
  }

  .leaflet-container {
    height: 100%;
    width: 100%;
    z-index: 1;
  }
}

// Marker styling
:host ::ng-deep {
  .leaflet-marker-icon {
    filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.3));
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.1);
    }
  }
}
