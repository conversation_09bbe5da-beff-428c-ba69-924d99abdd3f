.profile-page {
  min-height: 100vh;
  padding: 1rem;

  .profile-picture-container {
    position: relative;
    display: inline-block;

    c-avatar, .profile-avatar-large {
      border: 3px solid var(--cui-border-color);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      border-radius: 50% !important;
    }

    .profile-avatar-large {
      width: 150px !important;
      height: 150px !important;
      border-width: 4px;
    }

    &:hover c-avatar { opacity: 0.8; }
    &:hover .profile-avatar-large { transform: scale(1.05); }
  }

  .form-control-plaintext {
    padding: 0.375rem 0;
    margin-bottom: 0;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--cui-body-color);
    background-color: transparent;
    border: solid transparent;
    border-width: 1px 0;
    min-height: 38px;
    display: flex;
    align-items: center;
  }

  .form-control.is-invalid,
  input.is-invalid {
    background-image: none !important;
    padding: 0.375rem 0.75rem !important;
    border-color: var(--cui-form-invalid-border-color);
    box-shadow: 0 0 0 0.2rem rgba(var(--cui-danger-rgb), 0.25);
  }

  .invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: var(--cui-form-invalid-color);
  }

  .password-input {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }

  .password-toggle-btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 0;
    background-color: var(--cui-input-group-addon-bg);
    border-color: var(--cui-input-border-color);
    color: var(--cui-body-color);
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:hover {
      background-color: var(--cui-gray-200);
      color: var(--cui-primary);
    }

    &:focus {
      outline: 2px solid var(--cui-primary);
      outline-offset: -2px;
      z-index: 3;
    }

    i {
      font-size: 1rem;
      transition: transform 0.2s ease;
    }

    &:active i { transform: scale(0.95); }
  }

  .input-group .password-input {
    flex: 1;
    min-width: 0;

    &.is-invalid ~ .password-toggle-btn {
      border-color: var(--cui-form-invalid-border-color);
    }

    &:focus ~ .password-toggle-btn {
      border-color: var(--cui-input-focus-border-color);
    }
  }

  c-card {
    border: 1px solid var(--cui-border-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s ease;

    &:hover { box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); }
  }

  c-card-header {
    background-color: var(--cui-card-cap-bg);
    border-bottom: 1px solid var(--cui-border-color);

    h5 {
      color: var(--cui-heading-color);
      font-weight: 600;
    }

    i { color: var(--cui-primary); }
  }

  button {
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &:focus {
      outline: 2px solid var(--cui-primary);
      outline-offset: 2px;
    }
  }

  c-alert {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    i { font-size: 1.1rem; }
  }

  c-badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
  }

  @media (max-width: 768px) {
    padding: 0.5rem;

    .profile-picture-container {
      margin-bottom: 1rem;

      .profile-avatar-large {
        width: 120px !important;
        height: 120px !important;
      }
    }

    .d-flex.justify-content-between {
      flex-direction: column;
      gap: 1rem;

      .d-flex.gap-2 { justify-content: center; }
    }

    c-card { margin-bottom: 1rem; }
    .col-md-6 { margin-bottom: 1rem; }
  }

  @media (max-width: 576px) {
    .row { margin: 0; }
    .col-lg-8, .col-lg-4 { padding: 0; }
    c-card-body { padding: 1rem; }

    .profile-picture-container {
      text-align: center;
      margin-bottom: 1.5rem;

      .profile-avatar-large {
        width: 100px !important;
        height: 100px !important;
      }
    }

    .d-flex.gap-2 {
      flex-direction: column;
      gap: 0.5rem;

      button { width: 100%; }
    }

    .password-toggle-btn {
      min-width: 40px;
      padding: 0.375rem 0.5rem;

      i { font-size: 0.9rem; }
    }
  }

  [data-coreui-theme="dark"] & {
    c-card {
      background-color: var(--cui-card-bg);
      border-color: var(--cui-border-color);

      c-card-header {
        background-color: var(--cui-card-cap-bg);
        border-bottom-color: var(--cui-border-color);
      }
    }

    .profile-picture-container c-avatar {
      border-color: var(--cui-border-color);
    }

    .form-control-plaintext {
      color: var(--cui-body-color);
    }

    .password-toggle-btn {
      background-color: var(--cui-input-group-addon-bg);
      border-color: var(--cui-input-border-color);
      color: var(--cui-body-color);

      &:hover {
        background-color: var(--cui-gray-700);
        color: var(--cui-primary);
      }
    }
  }

  :host-context([dir="rtl"]) {
    .password-input {
      border-top-left-radius: 0 !important;
      border-bottom-left-radius: 0 !important;
      border-top-right-radius: var(--cui-border-radius) !important;
      border-bottom-right-radius: var(--cui-border-radius) !important;
      text-align: right;
      direction: rtl;
    }

    .password-toggle-btn {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      border-top-left-radius: var(--cui-border-radius);
      border-bottom-left-radius: var(--cui-border-radius);
      border-right: 0;
      border-left: 1px solid var(--cui-input-border-color);
      order: -1;

      i { transform: scaleX(-1); }
    }

    .input-group { flex-direction: row-reverse; }
  }
}
