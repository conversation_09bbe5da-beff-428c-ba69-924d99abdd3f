<div class="container mt-4">
  <h2>{{ 'nurse.title' | translate | async }}</h2>

  <!-- Add Nurse Button -->
  <div class="mb-3" *ngIf="!showForm">
    <app-action-button
      color="primary"
      icon="cilPlus"
      text="nurse.add"
      shape="rounded-pill"
      (clicked)="showAddForm()"
    ></app-action-button>
  </div>

  <!-- Nurse Form -->
  <form *ngIf="showForm" [formGroup]="form" (ngSubmit)="submit()" class="mb-4 card p-3 shadow-sm">
    <div class="row g-3">
      <!-- Personal Information Section -->
      <div class="col-12">
        <h5 class="mb-3">{{ 'nurse.personalInfo' | translate | async }}</h5>
      </div>

      <!-- Profile Picture Section -->
      <div class="col-12 mb-4">
        <label class="form-label">{{ 'nurse.profilePicture' | translate | async }}</label>
        <div class="d-flex align-items-center gap-3">
          <div class="position-relative" style="width: 100px; height: 100px;">
            <img
              [src]="imagePreview || form.get('imageUrl')?.value || defaultAvatarPath"
              alt="Profile Preview"
              class="rounded-circle w-100 h-100"
              style="object-fit: cover;"
              (error)="handleImageError($event)"
            >
            <div class="position-absolute top-0 end-0" *ngIf="imagePreview || form.get('imageUrl')?.value">
              <button
                type="button"
                class="btn btn-danger btn-sm rounded-circle"
                style="width: 24px; height: 24px; padding: 0;"
                (click)="clearImage()"
              >
                <i class="cil-x" style="font-size: 12px;"></i>
              </button>
            </div>
          </div>
          <div class="flex-grow-1">
            <input
              type="file"
              class="form-control"
              accept="image/*"
              (change)="onImageSelected($event)"
              #fileInput
            >
            <small class="text-muted d-block mt-1">
              {{ 'nurse.imageRequirements' | translate | async }}
            </small>
          </div>
        </div>
      </div>

      <!-- Name Fields -->
      <div class="col-12 col-md-6">
        <label class="form-label">{{ 'nurse.firstName' | translate | async }}</label>
        <input
          formControlName="firstName"
          placeholder="{{ 'nurse.firstName' | translate | async }}"
          class="form-control rounded-pill"
          [ngClass]="{'is-invalid': isFieldInvalid('firstName')}"
        />
        <div class="invalid-feedback" *ngIf="isFieldInvalid('firstName')">
          {{ getErrorMessage('firstName') | translate | async }}
        </div>
      </div>

      <div class="col-12 col-md-6">
        <label class="form-label">{{ 'nurse.lastName' | translate | async }}</label>
        <input
          formControlName="lastName"
          placeholder="{{ 'nurse.lastName' | translate | async }}"
          class="form-control rounded-pill"
          [ngClass]="{'is-invalid': isFieldInvalid('lastName')}"
        />
        <div class="invalid-feedback" *ngIf="isFieldInvalid('lastName')">
          {{ getErrorMessage('lastName') | translate | async }}
        </div>
      </div>

      <!-- Contact Information -->
      <div class="col-12 col-md-6">
        <label class="form-label">{{ 'nurse.email' | translate | async }}</label>
        <input
          formControlName="email"
          type="email"
          placeholder="{{ 'nurse.email' | translate | async }}"
          class="form-control rounded-pill"
          [ngClass]="{'is-invalid': isFieldInvalid('email')}"
          [readonly]="isEditMode"
          [disabled]="isEditMode"
        />
        <div class="invalid-feedback" *ngIf="isFieldInvalid('email')">
          {{ getErrorMessage('email') | translate | async }}
        </div>
      </div>

      <div class="col-12 col-md-6">
        <label class="form-label">{{ 'nurse.phone' | translate | async }}</label>
        <input
          formControlName="phoneNumber"
          placeholder="{{ 'nurse.phone' | translate | async }}"
          class="form-control rounded-pill"
          [ngClass]="{'is-invalid': isFieldInvalid('phoneNumber')}"
        />
        <div class="invalid-feedback" *ngIf="isFieldInvalid('phoneNumber')">
          {{ getErrorMessage('phoneNumber') | translate | async }}
        </div>
      </div>

      <!-- Password Section (Only for new nurses) -->
      <div class="col-12" *ngIf="!isEditMode">
        <h5 class="mb-3 mt-2">{{ 'nurse.accountCredentials' | translate | async }}</h5>
      </div>

      <div class="col-12 col-md-6" *ngIf="!isEditMode">
        <label class="form-label">{{ 'nurse.password' | translate | async }}</label>
        <div class="input-group">
          <input
            [type]="showPassword ? 'text' : 'password'"
            formControlName="password"
            placeholder="{{ 'nurse.password' | translate | async }}"
            class="form-control rounded-pill rounded-end"
            [ngClass]="{'is-invalid': isFieldInvalid('password')}"
          />
          <button class="btn btn-outline-secondary rounded-pill rounded-start" type="button" (click)="togglePasswordVisibility()">
            <app-bootstrap-icon [name]="showPassword ? 'eye-slash' : 'eye'"></app-bootstrap-icon>
          </button>
          <div class="invalid-feedback" *ngIf="isFieldInvalid('password')">
            {{ getErrorMessage('password') | translate | async }}
          </div>
        </div>
      </div>

      <div class="col-12 col-md-6" *ngIf="!isEditMode">
        <label class="form-label">{{ 'nurse.confirmPassword' | translate | async }}</label>
        <div class="input-group">
          <input
            [type]="showConfirmPassword ? 'text' : 'password'"
            formControlName="confirmPassword"
            placeholder="{{ 'nurse.confirmPassword' | translate | async }}"
            class="form-control rounded-pill rounded-end"
            [ngClass]="{'is-invalid': isFieldInvalid('confirmPassword')}"
          />
          <button class="btn btn-outline-secondary rounded-pill rounded-start" type="button" (click)="toggleConfirmPasswordVisibility()">
            <app-bootstrap-icon [name]="showConfirmPassword ? 'eye-slash' : 'eye'"></app-bootstrap-icon>
          </button>
          <div class="invalid-feedback" *ngIf="isFieldInvalid('confirmPassword')">
            {{ getErrorMessage('confirmPassword') | translate | async }}
          </div>
        </div>
      </div>

      <!-- Professional Information Section -->
      <div class="col-12">
        <h5 class="mb-3 mt-2">{{ 'nurse.professionalInfo' | translate | async }}</h5>
      </div>

      <!-- Specialization Dropdown -->
      <div class="col-12 col-md-6">
        <label class="form-label">{{ 'nurse.specialization' | translate | async }}</label>
        <select
          formControlName="specialtyId"
          class="form-select rounded-pill"
          [ngClass]="{'is-invalid': isFieldInvalid('specialtyId')}"
        >
          <option value="" disabled selected>{{ 'nurse.selectSpecialization' | translate | async }}</option>
          <option *ngFor="let specialty of specialties" [value]="specialty.id">
            {{ specialty.nameEn }}
          </option>
        </select>
        <div class="invalid-feedback" *ngIf="isFieldInvalid('specialtyId')">
          {{ getErrorMessage('specialtyId') | translate | async }}
        </div>
      </div>

      <!-- Governorate Dropdown -->
      <div class="col-12 col-md-6">
        <label class="form-label">{{ 'nurse.governorate' | translate | async }}</label>
        <select
          formControlName="governorateId"
          class="form-select rounded-pill"
          [ngClass]="{'is-invalid': isFieldInvalid('governorateId')}"
        >
          <option value="" disabled selected>{{ 'nurse.selectGovernorate' | translate | async }}</option>
          <option *ngFor="let governorate of governorates" [value]="governorate.id">
            {{ governorate.nameEn }}
          </option>
        </select>
        <div class="invalid-feedback" *ngIf="isFieldInvalid('governorateId')">
          {{ getErrorMessage('governorateId') | translate | async }}
        </div>
      </div>

      <!-- City Dropdown -->
      <div class="col-12 col-md-6">
        <label class="form-label">{{ 'nurse.city' | translate | async }}</label>
        <select
          formControlName="cityId"
          class="form-select rounded-pill"
          [ngClass]="{'is-invalid': isFieldInvalid('cityId')}"
          [disabled]="!form.get('governorateId')?.value"
        >
          <option value="" disabled selected>{{ 'nurse.selectCity' | translate | async }}</option>
          <option *ngFor="let city of cities" [value]="city.id">
            {{ city.nameEn }}
          </option>
        </select>
        <div class="invalid-feedback" *ngIf="isFieldInvalid('cityId')">
          {{ getErrorMessage('cityId') | translate | async }}
        </div>
      </div>

      <div class="col-12 col-md-6">
        <label class="form-label">{{ 'nurse.license' | translate | async }}</label>
        <input
          formControlName="medicalLicense"
          placeholder="{{ 'nurse.license' | translate | async }}"
          class="form-control rounded-pill"
          [ngClass]="{'is-invalid': isFieldInvalid('medicalLicense')}"
        />
        <div class="invalid-feedback" *ngIf="isFieldInvalid('medicalLicense')">
          {{ getErrorMessage('medicalLicense') | translate | async }}
        </div>
      </div>

      <!-- Location Information Section -->
      <div class="col-12">
        <h5 class="mb-3 mt-2">{{ 'nurse.locationInfo' | translate | async }}</h5>
      </div>

      <!-- Map Section -->
      <div class="col-12">
        <h5 class="mb-3 mt-2">{{ 'nurse.mapLocation' | translate | async }}</h5>
        <div class="d-flex justify-content-between align-items-center mb-2">
          <button type="button" class="btn btn-sm btn-outline-primary rounded-pill" (click)="toggleMap()">
            {{ showMap ? ('map.hideMap' | translate | async) : ('map.showMap' | translate | async) }}
          </button>
        </div>

        <div class="map-wrapper mb-3" [ngClass]="{'d-block': showMap, 'd-none': !showMap}">
          <app-map-selector
            [initialLatitude]="form.get('latitude')?.value"
            [initialLongitude]="form.get('longitude')?.value"
            [visible]="showMap"
            (locationSelected)="onLocationSelected($event)"
            #mapSelector
          ></app-map-selector>
        </div>

        <div class="row g-2" *ngIf="form.get('latitude')?.value || form.get('longitude')?.value">
          <div class="col-6">
            <div class="input-group input-group-sm">
              <span class="input-group-text">{{ 'map.latitude' | translate | async }}</span>
              <input type="text" class="form-control" [value]="form.get('latitude')?.value" readonly />
            </div>
          </div>
          <div class="col-6">
            <div class="input-group input-group-sm">
              <span class="input-group-text">{{ 'map.longitude' | translate | async }}</span>
              <input type="text" class="form-control" [value]="form.get('longitude')?.value" readonly />
            </div>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="col-12">
        <div class="mt-3 d-flex flex-column flex-md-row gap-2">
          <app-action-button
            color="primary"
            type="submit"
            [icon]="isEditMode ? 'cilPencil' : 'cilPlus'"
            [text]="isEditMode ? 'nurse.edit' : 'common.add'"
            shape="rounded-pill"
            [disabled]="form.invalid"
          ></app-action-button>

          <app-action-button
            color="secondary"
            icon="cilX"
            text="common.cancel"
            shape="rounded-pill"
            (clicked)="cancel()"
          ></app-action-button>
        </div>
      </div>
    </div>
  </form>

  <div *ngIf="successMessage" class="alert alert-success">{{ successMessage | translate | async }}</div>
  <div *ngIf="errorMessage" class="alert alert-danger">{{ errorMessage | translate | async }}</div>

  <div class="table-container">

    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>{{ 'nurse.image' | translate | async }}</th>
            <th>{{ 'nurse.name' | translate | async }}</th>
            <th>{{ 'nurse.phone' | translate | async }}</th>
            <th>{{ 'nurse.email' | translate | async }}</th>
            <th>{{ 'nurse.specialization' | translate | async }}</th>
            <th>{{ 'nurse.governorate' | translate | async }}</th>
            <th>{{ 'nurse.city' | translate | async }}</th>
            <th>{{ 'nurse.license' | translate | async }}</th>
            <th class="actions-cell">{{ 'nurse.actions' | translate | async }}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let nurse of nurses">
            <td>
              <img
                [src]="nurse.imageUrl"
                alt="nurse"
                class="avatar"
                (error)="handleImageError($event)"
              >
            </td>
            <td>{{ nurse.firstName }} {{ nurse.lastName }}</td>
            <td>{{ nurse.phoneNumber }}</td>
            <td>{{ nurse.email }}</td>
            <td>{{ nurse.specialization }}</td>
            <td>{{ nurse.governorate }}</td>
            <td>{{ nurse.city }}</td>
            <td>{{ nurse.licenseNumber }}</td>
            <td class="actions-cell">
              <div class="d-flex gap-2">
                <app-action-button
                  color="primary"
                  size="sm"
                  icon="cilPencil"
                  text="nurse.edit"
                  shape="rounded-pill"
                  (clicked)="editNurse(nurse)"
                ></app-action-button>

                <app-action-button
                  color="danger"
                  size="sm"
                  icon="cilTrash"
                  text="nurse.delete"
                  shape="rounded-pill"
                  (clicked)="openDeleteModal(nurse.id)"
                ></app-action-button>

                <app-action-button
                  color="info"
                  size="sm"
                  icon="cilCommentBubble"
                  text="nurse.reviews"
                  shape="rounded-pill"
                  (clicked)="openReviewsModal(nurse.id)"
                ></app-action-button>
              </div>
            </td>
          </tr>
          <tr *ngIf="nurses.length === 0" class="empty-row">
            <td colspan="10">{{ 'nurse.noData' | translate | async }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Delete Confirmation Modal (Bootstrap) -->
  <div class="modal fade show" tabindex="-1"
       [ngStyle]="{display: isDeleteModalOpen ? 'block' : 'none', background: 'rgba(0,0,0,0.4)'}"
       style="z-index: 2000; position: fixed; top: 0; left: 0; width: 100vw; height: 100vh;">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header bg-light">
          <h5 class="modal-title">{{ 'nurse.delete' | translate | async }}</h5>
          <button type="button" class="btn-close" (click)="closeDeleteModal()"></button>
        </div>
        <div class="modal-body">
          <p>{{ 'nurse.confirmDelete' | translate | async }}</p>
        </div>
        <div class="modal-footer">
          <app-action-button
            color="secondary"
            icon="cilX"
            text="common.cancel"
            (clicked)="closeDeleteModal()"
          ></app-action-button>

          <app-action-button
            color="danger"
            icon="cilTrash"
            text="nurse.delete"
            (clicked)="confirmDelete()"
          ></app-action-button>
        </div>
      </div>
    </div>
  </div>

  <!-- Standardized Pagination -->
  <app-pagination
    [currentPage]="pageNumber"
    [totalItems]="totalCount"
    [pageSize]="pageSize"
    [layout]="'simple'"
    [showInfo]="true"
    [size]="'md'"
    (pageChange)="onPageChange($event)"
  ></app-pagination>
</div>

<!-- Reviews Modal -->
<div class="modal fade show" tabindex="-1"
     [ngStyle]="{display: isReviewsModalOpen ? 'block' : 'none', background: 'rgba(0,0,0,0.4)'}"
     style="z-index: 2050; position: fixed; top: 0; left: 0; width: 100vw; height: 100vh;">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-header bg-light">
        <h5 class="modal-title">{{ 'nurse.reviewsFor' | translate | async }} {{ selectedNurseName }}</h5>
        <button type="button" class="btn-close" (click)="closeReviewsModal()"></button>
      </div>
      <div class="modal-body">
        <div *ngIf="selectedNurseReviews.length > 0; else noReviews">
          <ul class="list-group">
            <li *ngFor="let review of selectedNurseReviews" class="list-group-item">
              <div class="d-flex w-100 justify-content-between align-items-center">
              <div>
                <h6 class="mb-1 d-inline-block me-2">{{ review.patientName }}</h6>
                <small class="text-muted">{{ 'nurse.rating' | translate | async }}: {{ review.rating }}/5</small>
              </div>
              <app-action-button
                [color]="review.isUsedInPublic ? 'danger' : 'success'"
                [icon]="review.isUsedInPublic ? 'cilTrash' : 'cilPlus'"
                [text]="''"
                (clicked)="addToLandingPage(review)"
                size="sm"
                shape="rounded-pill"

              ></app-action-button>
              </div>
              <p class="mb-1 mt-2">{{ review.comment }}</p>
            </li>
          </ul>

        </div>
        <ng-template #noReviews>
          <p>{{ 'nurse.noReviews' | translate | async }}</p>
        </ng-template>
      </div>
      <div class="modal-footer">
        <app-action-button
          color="secondary"
          icon="cilX"
          text="common.close"
          (clicked)="closeReviewsModal()"
        ></app-action-button>

      </div>
    </div>
  </div>
</div>
