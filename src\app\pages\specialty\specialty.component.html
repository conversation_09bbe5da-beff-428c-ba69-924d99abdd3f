<div class="container-fluid py-4">
  <div class="card custom-card shadow">
    <div class="card-header bg-primary text-white rounded-top-4 d-flex justify-content-between align-items-center">
      <h4 class="mb-0 fw-bold">{{ 'specialty.title' | translate | async }}</h4>
      <!-- Add Specialty Button -->
      <app-action-button
        *ngIf="!showForm && !isEditMode"
        text="specialty.add"
        icon="cilPlus"
        color="light"
        (clicked)="showAddForm()"
      ></app-action-button>
    </div>
    <div class="card-body p-4">
      <!-- Feedback Messages -->
      <div *ngIf="successMessage" class="alert alert-success rounded-3 d-flex align-items-center mb-4">
        <i class="bi bi-check-circle-fill me-2 flex-shrink-0"></i>
        <div>{{ successMessage | translate | async }}</div>
      </div>
      <div *ngIf="errorMessage" class="alert alert-danger rounded-3 d-flex align-items-center mb-4">
        <i class="bi bi-exclamation-triangle-fill me-2 flex-shrink-0"></i>
        <div>{{ errorMessage | translate | async }}</div>
      </div>

      <!-- Loading Spinner -->
      <div *ngIf="isLoading" class="d-flex justify-content-center my-4">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">{{ 'common.loading' | translate | async }}</span>
        </div>
        <p class="text-muted ms-2 mb-0">{{ 'common.loading' | translate | async }}</p>
      </div>

      <!-- Specialty Form -->
      <form *ngIf="showForm" [formGroup]="form" (ngSubmit)="submit()" class="mb-4 animated fadeIn">
        <div class="row g-3 align-items-center">
          <div class="col-12 col-md-6 mb-3">
            <label class="form-label fw-semibold">{{ 'specialty.nameAr' | translate | async }}</label>
            <input formControlName="nameAr" [placeholder]="('specialty.nameAr' | translate | async)" class="form-control rounded-pill" [ngClass]="{'is-invalid': form.get('nameAr')?.invalid && form.get('nameAr')?.touched}" />
            <div class="invalid-feedback" *ngIf="form.get('nameAr')?.invalid && form.get('nameAr')?.touched">
              {{ 'specialty.nameArRequired' | translate | async }}
            </div>
          </div>
          <div class="col-12 col-md-6 mb-3">
            <label class="form-label fw-semibold">{{ 'specialty.nameEn' | translate | async }}</label>
            <input formControlName="nameEn" [placeholder]="('specialty.nameEn' | translate | async)" class="form-control rounded-pill" [ngClass]="{'is-invalid': form.get('nameEn')?.invalid && form.get('nameEn')?.touched}" />
            <div class="invalid-feedback" *ngIf="form.get('nameEn')?.invalid && form.get('nameEn')?.touched">
              {{ 'specialty.nameEnRequired' | translate | async }}
            </div>
          </div>
          <div class="col-12 col-md-6 mb-3">
            <label class="form-label fw-semibold">{{ 'specialty.descriptionAr' | translate | async }}</label>
            <input formControlName="descriptionAr" [placeholder]="('specialty.descriptionAr' | translate | async)" class="form-control rounded-pill" />
          </div>
          <div class="col-12 col-md-6 mb-3">
            <label class="form-label fw-semibold">{{ 'specialty.descriptionEn' | translate | async }}</label>
            <input formControlName="descriptionEn" [placeholder]="('specialty.descriptionEn' | translate | async)" class="form-control rounded-pill" />
          </div>
        </div>
        <div class="mt-3 d-flex flex-column flex-md-row gap-2">
          <app-action-button
            color="primary"
            type="submit"
            [icon]="isEditMode ? 'cilPencil' : 'cilPlus'"
            [text]="isEditMode ? 'specialty.edit' : 'specialty.add'"
            [disabled]="form.invalid || isLoading"
          ></app-action-button>

          <app-action-button
            *ngIf="isEditMode || showForm"
            color="secondary"
            icon="cilX"
            text="common.cancel"
            (clicked)="cancel()"
          ></app-action-button>
        </div>
      </form>

      <!-- Specialties Table -->
      <div *ngIf="!isLoading" class="table-container">
        <div class="table-responsive custom-scrollbar">
          <table class="custom-table table-hover">
            <thead>
              <tr>
                <th>{{ 'specialty.nameAr' | translate | async }}</th>
                <th>{{ 'specialty.nameEn' | translate | async }}</th>
                <th>{{ 'specialty.descriptionAr' | translate | async }}</th>
                <th>{{ 'specialty.descriptionEn' | translate | async }}</th>
                <th class="text-center">{{ 'specialty.actions' | translate | async }}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let specialty of specialties" class="hover-lift">
                <td>{{ specialty.nameAr }}</td>
                <td>{{ specialty.nameEn }}</td>
                <td>{{ specialty.descriptionAr }}</td>
                <td>{{ specialty.descriptionEn }}</td>
                <td class="text-center">
                  <div class="d-flex flex-column flex-md-row gap-2 align-items-stretch justify-content-center">
                    <app-action-button
                      color="primary"
                      size="sm"
                      icon="cilPencil"
                      text="specialty.edit"
                      shape = "rounded-pill"
                      (clicked)="edit(specialty)"
                    ></app-action-button>

                    <app-action-button
                      color="danger"
                      size="sm"
                      icon="cilTrash"
                      text="specialty.delete"
                      shape = "rounded-pill"
                      (clicked)="delete(specialty.id!)"
                    ></app-action-button>
                  </div>
                </td>
              </tr>
              <tr *ngIf="specialties.length === 0" class="empty-row">
                <td colspan="5" class="text-center p-4">
                  <div class="empty-state">
                    <i class="bi bi-heart-pulse mb-3" style="font-size: 4rem;"></i>
                    <h5>{{ 'specialty.noData' | translate | async }}</h5>
                    <p class="mb-0">{{ 'specialty.addSpecialtyToStart' | translate | async }}</p>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Delete Confirmation Modal -->
  <c-modal [visible]="isDeleteModalOpen" (visibleChange)="!$event && closeDeleteModal()" class="fade-in">
    <c-modal-header class="bg-light rounded-top">
      <div class="d-flex align-items-center">
        <div class="modal-icon-container bg-danger-subtle rounded-circle p-2 me-2">
          <i class="bi bi-trash-fill text-danger"></i>
        </div>
        <h5 cModalTitle class="mb-0 fw-bold">{{ 'specialty.delete' | translate | async }}</h5>
      </div>
      <button cButtonClose (click)="closeDeleteModal()"></button>
    </c-modal-header>
    <c-modal-body class="p-4">
      <div class="alert alert-warning">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        <span>{{ 'specialty.confirmDelete' | translate | async }}</span>
      </div>
    </c-modal-body>
    <c-modal-footer class="border-top-0">
      <app-action-button
        color="secondary"
        icon="cilX"
        text="common.cancel"
        (clicked)="closeDeleteModal()"
      ></app-action-button>

      <app-action-button
        color="danger"
        icon="cilTrash"
        text="specialty.delete"
        (clicked)="confirmDelete()"
      ></app-action-button>
    </c-modal-footer>
  </c-modal>
</div>
