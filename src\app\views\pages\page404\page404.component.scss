:host {
  display: block;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.error-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, var(--theme-primary, #CD2C4E) 0%, var(--theme-success, #40C5AA) 100%);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.language-toggle {
  position: absolute;
  top: 2rem;
  right: 2rem;
  z-index: 10;
}

.lang-switch-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

  &:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
  }
  &:active {
    transform: translateY(0);
  }
  i {
    font-size: 1.1rem;
  }
  .lang-text {
    font-weight: 600;
  }
}

// Background shapes
.background-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.bg-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  animation: float 6s ease-in-out infinite;

  &.shape-1 {
    width: 300px;
    height: 300px;
    top: -150px;
    left: -150px;
    animation-delay: 0s;
  }

  &.shape-2 {
    width: 200px;
    height: 200px;
    top: 50%;
    right: -100px;
    animation-delay: 2s;
  }

  &.shape-3 {
    width: 150px;
    height: 150px;
    bottom: -75px;
    left: 50%;
    animation-delay: 4s;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.error-content {
  position: relative;
  z-index: 2;
  width: 100%;
  padding: 2rem 1rem;
}

.error-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  padding: 3rem 2rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 500px;
  width: 100%;
  text-align: center;

  &:hover {
    transform: translateY(-3px);
  }
}

.error-header {
  margin-bottom: 2.5rem;
}

.logo-container {
  margin-bottom: 1.5rem;
}

.logo-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, var(--theme-primary, #CD2C4E) 0%, var(--theme-success, #40C5AA) 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  color: white;
  box-shadow: 0 8px 24px rgba(205, 44, 78, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }

  img {
    width: 40px;
    height: 40px;
    object-fit: contain;
  }
}

.error-number {
  font-size: 6rem;
  font-weight: 900;
  background: linear-gradient(135deg, var(--theme-primary, #CD2C4E) 0%, var(--theme-success, #40C5AA) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  margin-bottom: 1rem;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.error-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, var(--theme-primary, #CD2C4E) 0%, var(--theme-success, #40C5AA) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.error-subtitle {
  color: #718096;
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

.error-actions {
  margin-bottom: 2rem;
}

.search-section {
  margin-bottom: 2rem;
}

.modern-search {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(205, 44, 78, 0.2);

  .search-icon {
    background: linear-gradient(135deg, var(--theme-primary, #CD2C4E) 0%, var(--theme-success, #40C5AA) 100%);
    color: white;
    border: none;
    padding: 0.75rem 1rem;
  }

  .search-input {
    border: none;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    background: white;

    &:focus {
      box-shadow: none;
      border-color: transparent;
    }

    &::placeholder {
      color: #a0aec0;
      font-style: italic;
    }
  }

  .search-btn {
    background: linear-gradient(135deg, var(--theme-primary, #CD2C4E) 0%, var(--theme-success, #40C5AA) 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(205, 44, 78, 0.3);
    }

    i {
      font-size: 0.9rem;
    }
  }
}

.nav-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.home-btn {
  background: linear-gradient(135deg, var(--theme-primary, #CD2C4E) 0%, var(--theme-success, #40C5AA) 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(205, 44, 78, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(205, 44, 78, 0.4);
  }

  i {
    font-size: 1rem;
  }
}

.back-btn {
  background: transparent;
  color: var(--theme-primary, #CD2C4E);
  border: 2px solid var(--theme-primary, #CD2C4E);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;

  &:hover {
    background: var(--theme-primary, #CD2C4E);
    color: white;
    transform: translateY(-2px);
  }

  i {
    font-size: 1rem;
  }
}

.help-section {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 1.5rem;
  margin-top: 1.5rem;
}

.help-text {
  color: #718096;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  font-weight: 500;
}

.help-links {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.help-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--theme-primary, #CD2C4E);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 8px;

  &:hover {
    background: rgba(205, 44, 78, 0.1);
    transform: translateY(-1px);
    text-decoration: none;
    color: var(--theme-primary, #CD2C4E);
  }

  i {
    font-size: 1rem;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .error-number {
    font-size: 4rem;
  }

  .error-title {
    font-size: 1.5rem;
  }

  .error-card {
    padding: 2rem 1.5rem;
    margin: 1rem;
  }

  .nav-buttons {
    flex-direction: column;
    align-items: center;
  }

  .home-btn,
  .back-btn {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .help-links {
    flex-direction: column;
    align-items: center;
  }

  .modern-search {
    .search-btn {
      padding: 0.75rem 1rem;
      font-size: 0.9rem;
    }
  }
}

@media (max-width: 480px) {
  .error-number {
    font-size: 3rem;
  }

  .error-title {
    font-size: 1.25rem;
  }

  .error-card {
    padding: 1.5rem 1rem;
  }

  .language-toggle {
    top: 1rem;
    right: 1rem;
  }

  .lang-switch-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }
}
