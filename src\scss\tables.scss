/* Professional Table Styles for the entire application */

// Main table styles
.table {
  width: 100%;
  margin-bottom: 1.5rem;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  // Table headers
  thead {
    th {
      background: #f8fafc;
      color: #334155;
      font-weight: 600;
      text-transform: uppercase;
      font-size: 0.85rem;
      letter-spacing: 0.5px;
      padding: 1rem 1.25rem;
      border-bottom: 2px solid #e2e8f0;
      vertical-align: middle;
      position: relative;
      white-space: nowrap;

      &:first-child {
        border-top-left-radius: 8px;
      }

      &:last-child {
        border-top-right-radius: 8px;
      }

      // Optional sortable header elements
      &.sortable {
        cursor: pointer;

        &:hover {
          background: #f1f5f9;
        }

        &:after {
          content: '↕';
          margin-left: 5px;
          opacity: 0.5;
        }

        &.asc:after {
          content: '↑';
          opacity: 1;
        }

        &.desc:after {
          content: '↓';
          opacity: 1;
        }
      }
    }
  }

  // Table body
  tbody {
    tr {
      border-bottom: 1px solid #f1f5f9;
      transition: all 0.2s ease;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background-color: #f8fafc;
      }

      &.selected {
        background-color: rgba(59, 130, 246, 0.08);
      }
    }

    td {
      padding: 1rem 1.25rem;
      vertical-align: middle;
      color: #1e293b;

      // Adding subtle divider between cells
      &:not(:last-child) {
        border-right: 1px solid rgba(241, 245, 249, 0.5);
      }

      // Image styling within cells
      img.avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid white;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      }
    }

    // Empty state row
    tr.empty-row {
      td {
        padding: 2.5rem 1rem;
        text-align: center;
        color: #94a3b8;
        font-style: italic;
      }
    }
  }

  // Striped table variant
  &.table-striped {
    tbody tr:nth-of-type(odd) {
      background-color: #fafbfc;

      &:hover {
        background-color: #f8fafc;
      }
    }
  }

  // Hover table variant
  &.table-hover {
    tbody tr:hover {
      background-color: #f0f9ff !important;
    }
  }

  // Compact table variant
  &.table-sm {
    th, td {
      padding: 0.75rem 1rem;
      font-size: 0.95rem;
    }
  }

  // Actions column styling
  .actions-cell {
    white-space: nowrap;
    text-align: right;

    .btn {
      margin-left: 0.25rem;
    }
  }
}

// Table container
.table-container {
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 1.5rem;
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border-bottom: 1px solid #e2e8f0;

    h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #0f172a;
    }

    .table-actions {
      display: flex;
      gap: 0.5rem;
    }
  }

  .table-responsive {
    overflow-x: auto;

    .table {
      margin-bottom: 0;
      box-shadow: none;
      border-radius: 0;
    }
  }

  .table-footer {
    padding: 1rem 1.5rem;
    background: #fafbfc;
    border-top: 1px solid #f1f5f9;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .table-container {
    .table-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;

      .table-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }
  }

  .table {
    thead th, tbody td {
      padding: 0.75rem;
    }
  }
}

// RTL support
[dir="rtl"] {
  .table {
    thead th, tbody td {
      text-align: right;
    }

    .actions-cell {
      text-align: left;

      .btn {
        margin-left: 0;
        margin-right: 0.25rem;
      }
    }
  }
}
