import { Component, OnInit, inject } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { take } from 'rxjs/operators';
import { selectAuthResponse } from '../../store/auth/auth.selectors';
import { Roles } from '../../Enums/Roles.enum';

@Component({
  selector: 'app-auth-redirect',
  template: '<div>Redirecting...</div>',
  standalone: true
})
export class AuthRedirectComponent implements OnInit {
  private router = inject(Router);
  private store = inject(Store);

  ngOnInit(): void {
    // Check auth state and redirect appropriately
    this.store.select(selectAuthResponse).pipe(
      take(1)
    ).subscribe(authResponse => {
      console.log('AuthRedirectComponent: Checking auth state', authResponse);
      
      // Check if user is authenticated with valid token and admin role
      if (authResponse?.token && authResponse?.role === Roles.Admin) {
        console.log('AuthRedirectComponent: User authenticated, redirecting to profile');
        this.router.navigate(['/profile'], { replaceUrl: true });
      } else {
        console.log('AuthRedirectComponent: User not authenticated, redirecting to login');
        this.router.navigate(['/login'], { replaceUrl: true });
      }
    });
  }
}
