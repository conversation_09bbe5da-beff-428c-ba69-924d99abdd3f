/* RTL (Right-to-Left) Fixes for Arabic Language Support */

/* ===== GLOBAL SELECT DROPDOWN RTL FIXES ===== */

/* Universal select element targeting - Catches ALL possible select elements */
[dir="rtl"] *:is(
  select,
  .form-select,
  [cSelect],
  .custom-select,
  .bootstrap-select,
  .select2-selection,
  .ng-select,
  .mat-select,
  input[list],
  datalist
) {
  text-align: right !important;
  direction: rtl !important;
}

/* Base RTL direction for Arabic */
[dir="rtl"] {

  /* Universal Select dropdown styling - Covers ALL select elements */
  select,
  .form-select,
  [cSelect],
  select.form-control,
  select.form-select,
  .form-control[type="select"],
  input[type="select"],
  c-select select,
  c-form-select select,
  .custom-select,
  .bootstrap-select,
  .select2-selection,
  .ng-select,
  .mat-select {
    /* Fix text alignment */
    text-align: right !important;
    direction: rtl !important;

    /* Fix background arrow position for RTL */
    background-position: left 0.75rem center !important;
    padding-left: 2.25rem !important;
    padding-right: 0.75rem !important;

    /* Custom arrow for RTL - Light mode */
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-size: 16px 12px !important;

    /* Remove default appearance */
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
  }

  /* Specific fixes for different select types */
  select[multiple],
  .form-select[multiple] {
    background-image: none !important;
    padding-left: 0.75rem !important;
  }

  select[size]:not([size="1"]),
  .form-select[size]:not([size="1"]) {
    background-image: none !important;
    padding-left: 0.75rem !important;
  }

  /* Select options alignment - Universal coverage */
  select option,
  .form-select option,
  [cSelect] option,
  c-select option,
  .custom-select option,
  .bootstrap-select option,
  .ng-select .ng-option,
  .mat-option {
    text-align: right !important;
    direction: rtl !important;
    padding-right: 0.75rem !important;
    padding-left: 2rem !important;
  }

  /* Universal select states and variations */
  select,
  .form-select,
  [cSelect],
  c-select select,
  .custom-select,
  .bootstrap-select {

    /* Focus state */
    &:focus,
    &:focus-visible,
    &.focus {
      background-position: left 0.75rem center !important;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
      text-align: right !important;
      direction: rtl !important;
    }

    /* Multiple select */
    &[multiple],
    &.multiple {
      background-image: none !important;
      padding-left: 0.75rem !important;
      padding-right: 0.75rem !important;
    }

    /* Sized select */
    &[size]:not([size="1"]),
    &.sized {
      background-image: none !important;
      padding-left: 0.75rem !important;
      padding-right: 0.75rem !important;
    }

    /* Disabled state */
    &:disabled,
    &.disabled {
      background-position: left 0.75rem center !important;
      opacity: 0.65;
      text-align: right !important;
      direction: rtl !important;
    }

    /* Readonly state */
    &:read-only,
    &[readonly] {
      background-position: left 0.75rem center !important;
      text-align: right !important;
      direction: rtl !important;
    }
  }

  /* CoreUI component wrappers */
  c-select,
  c-form-select {
    select {
      text-align: right !important;
      direction: rtl !important;
      background-position: left 0.75rem center !important;
      padding-left: 2.25rem !important;
      padding-right: 0.75rem !important;
    }
  }

  /* Angular Material select fixes */
  .mat-select {
    text-align: right !important;
    direction: rtl !important;

    .mat-select-arrow {
      right: auto !important;
      left: 16px !important;
    }

    .mat-select-value {
      text-align: right !important;
    }
  }

  /* ng-select fixes */
  .ng-select {
    text-align: right !important;
    direction: rtl !important;

    .ng-arrow-wrapper {
      right: auto !important;
      left: 12px !important;
    }

    .ng-placeholder,
    .ng-value-container {
      text-align: right !important;
      padding-right: 12px !important;
      padding-left: 30px !important;
    }
  }

  /* Input group select fixes */
  .input-group .form-select {
    &:not(:first-child) {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      border-top-left-radius: var(--cui-border-radius);
      border-bottom-left-radius: var(--cui-border-radius);
    }

    &:not(:last-child) {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      border-top-right-radius: var(--cui-border-radius);
      border-bottom-right-radius: var(--cui-border-radius);
    }
  }

  /* Dropdown menu positioning */
  .dropdown-menu {
    text-align: right;
    direction: rtl;

    /* Fix dropdown item alignment */
    .dropdown-item {
      text-align: right;
      padding-left: 1.5rem;
      padding-right: 1rem;

      /* Icon positioning in dropdown items */
      i, svg {
        margin-left: 0.5rem;
        margin-right: 0;
        float: right;
      }
    }

    /* Dropdown header alignment */
    .dropdown-header {
      text-align: right;
      padding-left: 1.5rem;
      padding-right: 1rem;
    }

    /* Dropdown divider */
    .dropdown-divider {
      margin: 0.5rem 1rem 0.5rem 1.5rem;
    }
  }

  /* CoreUI dropdown specific fixes */
  c-dropdown {
    .dropdown-menu {
      text-align: right;

      [cDropdownItem] {
        text-align: right;
        direction: rtl;

        /* Fix icon positioning */
        i {
          margin-left: 0.5rem;
          margin-right: 0;
        }
      }
    }
  }

  /* Notification dropdown specific fixes */
  .notification-dropdown {
    .dropdown-menu {
      min-width: 320px;
      max-width: 400px;

      .notification-item {
        text-align: right;
        direction: rtl;

        .d-flex {
          flex-direction: row-reverse;

          .me-3 {
            margin-left: 1rem !important;
            margin-right: 0 !important;
          }

          .ms-2 {
            margin-right: 0.5rem !important;
            margin-left: 0 !important;
          }
        }
      }
    }
  }

  /* Form control fixes */
  .form-control {
    text-align: right;
    direction: rtl;
  }

  /* Input group fixes */
  .input-group {
    flex-direction: row-reverse;

    .input-group-text {
      &:first-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border-top-right-radius: var(--cui-border-radius);
        border-bottom-right-radius: var(--cui-border-radius);
      }

      &:last-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-top-left-radius: var(--cui-border-radius);
        border-bottom-left-radius: var(--cui-border-radius);
      }
    }

    .form-control {
      &:not(:first-child) {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }

      &:not(:last-child) {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }
  }

  /* Button group fixes */
  .btn-group {
    flex-direction: row-reverse;

    .btn {
      &:first-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border-top-right-radius: var(--cui-border-radius);
        border-bottom-right-radius: var(--cui-border-radius);
      }

      &:last-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-top-left-radius: var(--cui-border-radius);
        border-bottom-left-radius: var(--cui-border-radius);
      }
    }
  }

  /* Pagination fixes */
  .pagination {
    flex-direction: row-reverse;

    .page-link {
      &:first-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border-top-right-radius: var(--cui-border-radius);
        border-bottom-right-radius: var(--cui-border-radius);
      }

      &:last-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-top-left-radius: var(--cui-border-radius);
        border-bottom-left-radius: var(--cui-border-radius);
      }
    }
  }

  /* Badge positioning fixes */
  .badge {
    &.ms-auto {
      margin-left: 0 !important;
      margin-right: auto !important;
    }

    &.me-auto {
      margin-right: 0 !important;
      margin-left: auto !important;
    }
  }

  /* Universal Icon positioning fixes */
  .bi, i, svg, .icon {
    &.me-1 {
      margin-left: 0.25rem !important;
      margin-right: 0 !important;
    }

    &.me-2 {
      margin-left: 0.5rem !important;
      margin-right: 0 !important;
    }

    &.me-3 {
      margin-left: 1rem !important;
      margin-right: 0 !important;
    }

    &.ms-1 {
      margin-right: 0.25rem !important;
      margin-left: 0 !important;
    }

    &.ms-2 {
      margin-right: 0.5rem !important;
      margin-left: 0 !important;
    }

    &.ms-3 {
      margin-right: 1rem !important;
      margin-left: 0 !important;
    }
  }

  /* Flex container icon fixes */
  .d-flex {
    .bi, i, svg, .icon {
      &:first-child {
        margin-right: 0.5rem !important;
        margin-left: 0 !important;
      }

      &:last-child {
        margin-left: 0.5rem !important;
        margin-right: 0 !important;
      }
    }
  }

  /* Notification specific icon containers */
  .notification-icon,
  .icon-container,
  .notification-type-icon {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;

    i, svg, .bi {
      display: block !important;
      margin: 0 !important;
    }
  }

  /* Table fixes */
  .table {
    text-align: right;

    th, td {
      text-align: right;
    }

    .table-responsive {
      direction: rtl;
    }
  }

  /* Card fixes */
  .card {
    .card-header {
      text-align: right;
    }

    .card-body {
      text-align: right;
    }

    .card-footer {
      text-align: right;
    }
  }

  /* Breadcrumb fixes */
  .breadcrumb {
    flex-direction: row-reverse;

    .breadcrumb-item {
      &::before {
        content: "\\";
        transform: scaleX(-1);
      }

      &:first-child::before {
        content: none;
      }
    }
  }

  /* Modal fixes */
  .modal {
    .modal-header {
      text-align: right;

      .btn-close {
        margin-left: 0;
        margin-right: auto;
      }
    }

    .modal-body {
      text-align: right;
    }

    .modal-footer {
      text-align: right;
      justify-content: flex-start;

      .btn {
        margin-left: 0;
        margin-right: 0.5rem;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  /* Sidebar fixes */
  .sidebar {
    .sidebar-nav {
      text-align: right;

      .nav-link {
        text-align: right;

        .nav-icon {
          margin-left: 0.5rem;
          margin-right: 0;
          float: right;
        }
      }
    }
  }

  /* Navbar fixes */
  .navbar {
    .navbar-nav {
      flex-direction: row-reverse;

      .nav-link {
        text-align: right;
      }
    }

    .navbar-brand {
      margin-left: 0;
      margin-right: 1rem;
    }
  }
}

/* Dark mode RTL fixes - Universal coverage */
[dir="rtl"][data-coreui-theme="dark"] {
  select,
  .form-select,
  [cSelect],
  c-select select,
  c-form-select select,
  .custom-select,
  .bootstrap-select,
  .form-control[type="select"] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
    background-position: left 0.75rem center !important;
    text-align: right !important;
    direction: rtl !important;

    &:focus,
    &:focus-visible,
    &.focus {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
      background-position: left 0.75rem center !important;
      text-align: right !important;
      direction: rtl !important;
    }

    &:disabled,
    &.disabled {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23666666' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
      background-position: left 0.75rem center !important;
    }
  }

  /* Dark mode for Angular Material */
  .mat-select {
    .mat-select-arrow {
      color: #ffffff !important;
    }
  }

  /* Dark mode for ng-select */
  .ng-select {
    .ng-arrow-wrapper .ng-arrow {
      border-color: #ffffff transparent transparent !important;
    }
  }
}

/* Specific component fixes */
[dir="rtl"] {

  /* Universal select fixes for all pages and components */
  .admin-management,
  .pharmacy-management,
  .notifications-page,
  .dashboard,
  .reports,
  .reservations,
  .requests,
  .service-categories,
  .locations {

    select,
    .form-select,
    [cSelect],
    .custom-select {
      text-align: right !important;
      direction: rtl !important;
      background-position: left 0.75rem center !important;
      padding-left: 2.25rem !important;
      padding-right: 0.75rem !important;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
      background-repeat: no-repeat !important;
      background-size: 16px 12px !important;
      -webkit-appearance: none !important;
      -moz-appearance: none !important;
      appearance: none !important;

      &:focus {
        background-position: left 0.75rem center !important;
        text-align: right !important;
        direction: rtl !important;
      }

      option {
        text-align: right !important;
        direction: rtl !important;
      }
    }
  }

  /* Notifications page specific fixes */
  .notifications-page {
    .notification-card {

      /* Main card flex container */
      .d-flex {
        flex-direction: row-reverse !important;

        /* Icon container positioning */
        .me-3 {
          margin-left: 1rem !important;
          margin-right: 0 !important;
          order: 2; /* Move icon to the right */
        }

        .ms-2 {
          margin-right: 0.5rem !important;
          margin-left: 0 !important;
        }

        /* Content container */
        .flex-grow-1 {
          order: 1; /* Move content to the left */
          text-align: right !important;
        }
      }

      /* Notification icon container */
      .notification-icon {
        margin-left: 1rem !important;
        margin-right: 0 !important;
        order: 2 !important;
        flex-shrink: 0;

        /* Icon alignment within container */
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;

        i, svg {
          display: block;
          text-align: center;
        }
      }

      /* Notification body text */
      .notification-body {
        text-align: right !important;
        direction: rtl !important;
      }

      /* Header section with title and time */
      .d-flex.justify-content-between {
        flex-direction: row-reverse !important;
        text-align: right !important;

        h6 {
          text-align: right !important;
          order: 2;
        }

        .d-flex.align-items-center.gap-2 {
          flex-direction: row-reverse !important;
          order: 1;

          small {
            margin-left: 0.5rem !important;
            margin-right: 0 !important;
          }

          c-badge {
            margin-left: 0.5rem !important;
            margin-right: 0 !important;
          }
        }
      }

      /* Footer section with badge and actions */
      .d-flex.justify-content-between.align-items-center {
        flex-direction: row-reverse !important;

        .d-flex.align-items-center.gap-2 {
          flex-direction: row-reverse !important;
          order: 2;

          c-badge {
            margin-left: 0.5rem !important;
            margin-right: 0 !important;
          }

          small {
            margin-left: 0.5rem !important;
            margin-right: 0 !important;
          }
        }

        .d-flex.gap-2 {
          flex-direction: row-reverse !important;
          order: 1;

          button {
            margin-left: 0.5rem !important;
            margin-right: 0 !important;

            &:first-child {
              margin-left: 0 !important;
            }
          }

          span {
            margin-left: 0.5rem !important;
            margin-right: 0 !important;
          }
        }
      }
    }
  }

  /* Admin management page fixes */
  .admin-management {
    .table {
      th, td {
        text-align: right;

        &:first-child {
          text-align: center; // For checkboxes/actions
        }
      }
    }

    .btn-group {
      flex-direction: row-reverse;
    }
  }

  /* Pharmacy management page fixes */
  .pharmacy-management {
    .form-row {
      .col {
        text-align: right;
      }
    }

    .map-container {
      direction: ltr; // Keep map controls in LTR
    }
  }

  /* Search and filter components */
  .search-filters {
    .row {
      flex-direction: row-reverse;

      .col {
        text-align: right;
      }
    }

    .btn-group {
      flex-direction: row-reverse;
    }
  }

  /* CoreUI specific component fixes */
  c-card {
    .card-header {
      text-align: right;

      .d-flex {
        flex-direction: row-reverse;

        .ms-auto {
          margin-left: 0 !important;
          margin-right: auto !important;
        }
      }
    }
  }

  c-modal {
    .modal-header {
      flex-direction: row-reverse;

      .btn-close {
        margin-left: 0;
        margin-right: auto;
      }
    }

    .modal-footer {
      flex-direction: row-reverse;

      .btn {
        margin-left: 0;
        margin-right: 0.5rem;

        &:first-child {
          margin-right: 0;
        }
      }
    }
  }

  /* Alert component fixes */
  c-alert {
    text-align: right;

    .alert-dismissible {
      .btn-close {
        left: 0;
        right: auto;
      }
    }
  }

  /* Progress bar fixes */
  .progress {
    direction: ltr; // Keep progress direction LTR for consistency
  }

  /* Badge fixes */
  .badge {
    &.float-end {
      float: left !important;
    }

    &.float-start {
      float: right !important;
    }
  }
}

/* Responsive RTL fixes */
@media (max-width: 768px) {
  [dir="rtl"] {
    .dropdown-menu {
      min-width: 280px;

      .notification-item {
        .d-flex {
          flex-direction: column;
          align-items: flex-end;

          .me-3 {
            margin: 0 0 0.5rem 0 !important;
          }
        }
      }
    }

    .input-group {
      flex-direction: column;

      .form-control,
      .input-group-text {
        border-radius: var(--cui-border-radius) !important;
        margin-bottom: 0.5rem;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .btn-group {
      flex-direction: column;

      .btn {
        border-radius: var(--cui-border-radius) !important;
        margin-bottom: 0.25rem;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .table-responsive {
      .table {
        font-size: 0.875rem;

        th, td {
          padding: 0.5rem 0.25rem;
          white-space: nowrap;
        }
      }
    }
  }
}
