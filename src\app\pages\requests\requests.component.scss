.request-container {
  max-width: 1100px;
  margin: 32px auto;
  padding: 24px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.07);
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  margin-bottom: 24px;
  background: linear-gradient(to right, #f8fafc, #f1f5f9);
  padding: 18px 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
}

.filter-form label {
  font-weight: 500;
  color: #334155;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-form input[type="date"] {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background-color: white;
  color: #1e293b;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.filter-form input[type="date"]:focus {
  border-color: #0ea5e9;
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.15);
  outline: none;
}

.filter-form button {
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
}

.filter-form button:hover:not([disabled]) {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.12);
}

.filter-form button[disabled] {
  background: #cbd5e1;
  color: #64748b;
  cursor: not-allowed;
  box-shadow: none;
}

.loading {
  text-align: center;
  font-size: 1.2em;
  color: #3b82f6;
  margin: 32px 0;
}

.requests-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 18px;
  background: #fafbfc;
  border-radius: 8px;
  overflow: hidden;
}
.requests-table th, .requests-table td {
  padding: 10px 12px;
  text-align: left;
}
.requests-table th {
  background: #f1f5f9;
  color: #222;
  font-weight: 600;
  border-bottom: 2px solid #e5e7eb;
}
.requests-table tbody tr {
  border-bottom: 1px solid #e5e7eb;
  transition: background 0.2s;
}
.requests-table tbody tr:hover {
  background: #f0f9ff;
}
.requests-table img {
  vertical-align: middle;
}

/* Remove old pagination styles as they are now defined globally */
.pagination {
  /* Now using the global .simple-pagination styles */
  justify-content: flex-end;
}

@media (max-width: 800px) {
  .request-container {
    padding: 8px;
  }
  .filter-form {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  .requests-table th, .requests-table td {
    padding: 7px 6px;
    font-size: 0.97em;
  }
}

.details-btn {
  padding: 6px 14px;
  background: linear-gradient(135deg, #0ea5e9, #0284c7);
  color: #fff;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.95em;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(14, 165, 233, 0.3);
}
.details-btn:hover {
  background: linear-gradient(135deg, #0284c7, #075985);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(14, 165, 233, 0.4);
}

.details-modal {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.45);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}
.details-content {
  background: #fff;
  border-radius: 14px;
  padding: 32px 28px 24px 28px;
  min-width: 380px;
  max-width: 90vw;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
  position: relative;
  transform: translateY(0);
  transition: transform 0.3s ease;
}
.close-btn {
  position: absolute;
  top: 12px;
  right: 14px;
  background: transparent;
  border: none;
  font-size: 1.8em;
  color: #777;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
.close-btn:hover {
  color: #e11d48;
  background: rgba(225, 29, 72, 0.1);
}
.details-row {
  margin-bottom: 14px;
  font-size: 1.05em;
  display: flex;
  align-items: flex-start;
  padding-bottom: 8px;
  border-bottom: 1px dashed rgba(0,0,0,0.08);
}
.details-row strong {
  min-width: 130px;
  color: #0c4a6e;
  font-weight: 600;
}
.details-content h3 {
  margin-top: 0;
  margin-bottom: 24px;
  color: #0ea5e9;
  font-size: 1.4em;
  font-weight: 700;
  position: relative;
  padding-bottom: 10px;
}
.details-content h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #0ea5e9, transparent);
  border-radius: 3px;
}

/* Animation for modal */
@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-content {
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.25);
  border-radius: 16px;
  border: none;
}

.modal-header {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 20px 24px;
}

.modal-title {
  font-weight: 700;
  font-size: 1.25rem;
  color: #0f172a;
  letter-spacing: -0.02em;
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 16px 24px;
  background: #f8fafc;
}

.btn-primary {
  background: linear-gradient(135deg, #0ea5e9, #0284c7);
  border: none;
  box-shadow: 0 2px 5px rgba(14, 165, 233, 0.3);
  padding: 8px 24px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0284c7, #075985);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(14, 165, 233, 0.4);
}

/* Enhanced request details */
.details-container {
  position: relative;
  margin-top: 6px;
}

.request-id {
  margin-bottom: 20px;
  font-size: 1.1rem;
  color: #64748b;
  font-weight: 500;
}

.request-id span {
  font-weight: 700;
  color: #0c4a6e;
}

.nurse-info {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding: 18px;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(186, 230, 253, 0.5);
}

.nurse-avatar {
  width: 75px;
  height: 75px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid white;
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.2);
}

.nurse-details {
  margin-left: 16px;
}

.nurse-name {
  font-weight: 700;
  font-size: 1.15rem;
  color: #0c4a6e;
  margin-bottom: 4px;
}

.nurse-specialty {
  color: #64748b;
  font-size: 0.95rem;
}

.details-section {
  margin-bottom: 24px;
  padding-bottom: 14px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.details-row {
  margin-bottom: 14px;
  font-size: 1.05em;
  display: flex;
  align-items: flex-start;
  padding-bottom: 8px;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.06);
}

.details-row:last-child {
  border-bottom: none;
}

.details-row strong {
  min-width: 130px;
  color: #0f172a;
  font-weight: 600;
}

.status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  background: linear-gradient(135deg, #e0f2fe, #bae6fd);
  color: #0c4a6e;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.price {
  font-weight: 700;
  color: #0284c7;
}

.location-section {
  position: relative;
}

.location-section h6 {
  margin-bottom: 16px;
  font-weight: 700;
  color: #0f172a;
  font-size: 1.05rem;
  position: relative;
  display: inline-block;
}

.location-section h6::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #0ea5e9, transparent);
  border-radius: 2px;
}

/* Update map related styling */
.map-container {
  height: 250px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(14, 165, 233, 0.2);
  margin-bottom: 0;
}

/* Modal size updates - update this to be wider */
.modal-dialog {
  max-width: 850px;
  width: 95%;
  margin: 1.75rem auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal-body {
    padding: 20px;
  }

  .nurse-info {
    padding: 14px;
  }

  .map-container {
    height: 250px;
    margin-bottom: 15px;
  }

  .modal-header, .modal-footer {
    padding: 15px 20px;
  }
}

@media (max-width: 480px) {
  .modal-body {
    padding: 16px;
  }

  .nurse-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 12px;
  }

  .nurse-details {
    margin-left: 0;
    margin-top: 12px;
  }

  .nurse-avatar {
    width: 65px;
    height: 65px;
  }

  .details-row {
    flex-direction: column;
  }

  .details-row strong {
    margin-bottom: 4px;
    min-width: auto;
  }

  .map-container {
    height: 200px;
  }
}

/* Two-column layout for details and map */
.details-map-container {
  display: flex;
  flex-direction: row;
  gap: 20px;
  margin-bottom: 10px;
}

.details-column {
  flex: 1;
  min-width: 0; /* Allow for proper flex shrinking */
}

.map-column {
  flex: 1;
  min-width: 0; /* Allow for proper flex shrinking */
}

.map-column h6 {
  margin-bottom: 16px;
  font-weight: 700;
  color: #0f172a;
  font-size: 1.05rem;
  position: relative;
  display: inline-block;
}

.map-column h6::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #0ea5e9, transparent);
  border-radius: 2px;
}

/* Update map container for two-column layout */
.map-container {
  width: 100%;
  height: 250px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(14, 165, 233, 0.2);
}

/* Make modal wider to accommodate the two-column layout */
.modal-dialog {
  max-width: 800px;
  width: 90%;
  margin: 1.75rem auto;
}

/* Responsive adjustments for different screen sizes */
@media (max-width: 768px) {
  /* Switch to single column on tablet */
  .details-map-container {
    flex-direction: column;
    gap: 15px;
  }

  .map-container {
    height: 250px;
  }

  .modal-dialog {
    max-width: 650px;
  }
}

@media (max-width: 480px) {
  .map-container {
    height: 200px;
  }

  .nurse-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 12px;
  }

  .nurse-details {
    margin-left: 0;
    margin-top: 12px;
  }

  .nurse-avatar {
    width: 65px;
    height: 65px;
  }

  .details-row {
    flex-direction: column;
  }

  .details-row strong {
    margin-bottom: 4px;
    min-width: auto;
  }
}

/* Pagination container styling */
.pagination-container {
  display: flex;
  justify-content: center;
  margin: 1.5rem 0;
}

/* Responsive adjustments for different screen sizes */
@media (max-width: 768px) {
  /* Switch to single column on tablet */
  .details-map-container {
    flex-direction: column;
    gap: 15px;
  }

  .map-container {
    height: 250px;
  }

  .modal-dialog {
    max-width: 650px;
  }
}
