<c-row>
  <c-col xs="12">
    @for (pos of positions | slice : 1; track pos) {
      <c-toaster [ngClass]="'p-3'" position="fixed" placement="{{pos}}" />
    }
    <c-card>
      <c-card-header>
        Toaster
      </c-card-header>
      <c-card-body>
        <c-container>
          <c-row>
            <c-col lg="6" sm="12">
              <form [formGroup]="toasterForm" cForm>
                <h5>Add toast with following props:</h5>

                <c-form-check class="my-2 mt-4">
                  <input cFormCheckInput formControlName="autohide" id="autohide" type="checkbox">
                  <label cFormCheckLabel for="autohide">
                    Toast autohide
                  </label>
                </c-form-check>

                <c-input-group>
                  <span cInputGroupText>Delay</span>
                  <input cFormControl formControlName="delay" id="delay" [type]="'number'">
                </c-input-group>

                <c-input-group class="my-2">
                  <span cInputGroupText>Position</span>
                  <select cSelect formControlName="position">
                    @for (position of positions; track position) {
                      <option [ngValue]="position">
                        {{ position }}
                      </option>
                    }
                  </select>
                </c-input-group>

                <c-input-group class="my-2">
                  <span cInputGroupText>Color</span>
                  <select cSelect formControlName="color">
                    @for (color of colors; track color) {
                      <option [ngValue]="color">
                        {{ color }}
                      </option>
                    }
                  </select>
                </c-input-group>

                <c-form-check class="my-2">
                  <input cFormCheckInput formControlName="fade" id="fade" type="checkbox">
                  <label cFormCheckLabel for="fade">Fade</label>
                </c-form-check>

                <c-form-check class="my-2">
                  <input cFormCheckInput formControlName="closeButton" id="close" type="checkbox">
                  <label cFormCheckLabel for="close">Close Button</label>
                </c-form-check>
                <hr />
                <button (click)="addToast()" cButton class="m-1" color="success">
                  Add toast
                </button>
              </form>
            </c-col>
            <c-col lg="6" sm="12" class="mt-3" [ngStyle]="{position: 'relative'}">
              <c-toaster [placement]="positionStatic" [ngStyle]="{display: 'contents'}">
                <c-toast [autohide]="false" [fade]="true" [visible]="true" color="danger">
                  <c-toast-header>Toast title</c-toast-header>
                  <c-toast-body>This is a static toast in a static toaster</c-toast-body>
                </c-toast>
                <c-toast [fade]="true" [visible]="true" color="dark" >
                  <c-toast-header [closeButton]="false">Toast title</c-toast-header>
                  <c-toast-body cTextColor="secondary">This is a static toast in a static toaster</c-toast-body>
                </c-toast>
                <app-toast-simple #toast [autohide]="false" [fade]="true" [visible]="true" [title]="'App Toast'"
                                  color="secondary">
                  This is a toast in static positioned App toaster! {{ toast.index() }}
                </app-toast-simple>
              </c-toaster>
            </c-col>
          </c-row>
        </c-container>
      </c-card-body>
    </c-card>
    <c-card class="mt-3">
      <c-card-body>
        <c-row>
          <c-col>
            <p>Form value: {{ toasterForm.value | json }}</p>
          </c-col>
        </c-row>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>
