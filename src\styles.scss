/* You can add global styles to this file, and also import other style files */
@forward 'scss/styles.scss';

/* Import RTL fixes for Arabic language support */
@import 'scss/rtl-fixes';

/* Global RTL Select Fixes - High Priority */
[dir="rtl"] {

  /* Universal select targeting with highest specificity */
  select,
  .form-select,
  [cSelect],
  input[type="select"],
  .custom-select,
  .bootstrap-select {
    text-align: right !important;
    direction: rtl !important;
    background-position: left 0.75rem center !important;
    padding-left: 2.25rem !important;
    padding-right: 0.75rem !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-size: 16px 12px !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
  }

  /* Select options */
  select option,
  .form-select option {
    text-align: right !important;
    direction: rtl !important;
  }

  /* Focus states */
  select:focus,
  .form-select:focus,
  [cSelect]:focus {
    background-position: left 0.75rem center !important;
    text-align: right !important;
    direction: rtl !important;
  }
}

/* Dark mode select fixes */
[dir="rtl"][data-coreui-theme="dark"] {
  select,
  .form-select,
  [cSelect] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;

    &:focus {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
    }
  }
}

/* RTL Notification Icon Fixes - Global */
[dir="rtl"] {

  /* Notification cards and icon containers */
  .notification-card,
  .notification-item,
  .alert,
  .toast {

    .d-flex {
      flex-direction: row-reverse !important;

      .me-3,
      .me-2,
      .me-1 {
        margin-left: 1rem !important;
        margin-right: 0 !important;
        order: 2 !important;
      }

      .flex-grow-1 {
        order: 1 !important;
        text-align: right !important;
      }
    }

    .notification-icon,
    .icon-container,
    .alert-icon {
      margin-left: 1rem !important;
      margin-right: 0 !important;
      order: 2 !important;

      i, svg, .bi {
        margin: 0 !important;
        display: block !important;
      }
    }
  }

  /* Icon positioning in buttons and links */
  button, a, .btn {
    i, svg, .bi {
      &.me-1 {
        margin-left: 0.25rem !important;
        margin-right: 0 !important;
      }

      &.me-2 {
        margin-left: 0.5rem !important;
        margin-right: 0 !important;
      }
    }
  }

  /* Badge and small text icon fixes */
  .badge, .small, small {
    i, svg, .bi {
      &.me-1 {
        margin-left: 0.25rem !important;
        margin-right: 0 !important;
      }
    }
  }
}

/* Additional Angular Material styles if needed */
@use '@angular/material' as mat;

// Add any additional Angular Material theming if needed
// $custom-theme: mat.define-light-theme((
//   color: (
//     primary: $custom-primary,
//     accent: $custom-accent,
//     warn: $custom-warn,
//   )
// ));

// Include any additional global Angular styles here

// Global icon fixes - CRITICAL FIX FOR ICON VISIBILITY
svg[cIcon] {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  fill: currentColor !important;
  stroke: currentColor !important;
  z-index: 10 !important;
  pointer-events: auto !important;
  min-width: 16px !important;
  min-height: 16px !important;
}

// Force all c-icon elements to be visible and properly rendered
c-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 5 !important;
  min-width: 16px !important;
  min-height: 16px !important;

  &::before, &::after {
    display: none !important;
  }
}

// Fix CoreUI icon implementation issues
:root {
  --cui-icon-size-sm: 16px !important;
  --cui-icon-size-md: 20px !important;
  --cui-icon-size-lg: 24px !important;
  --cui-icon-size-xl: 32px !important;
}

// Add your other global styles below

/* RTL Support for Arabic text */
html[lang="ar"] {
  direction: rtl;
  text-align: right;

  .sidebar {
    right: 0;
    left: auto;
  }

  .header {
    padding-right: var(--cui-header-padding-x);
    padding-left: 0;
  }

  .header-toggler {
    margin-left: 0;
    margin-right: auto;
  }

  .wrapper {
    padding-inline: var(--cui-sidebar-occupy-end, 0) var(--cui-sidebar-occupy-start, 0);
  }

  .sidebar-brand {
    margin-right: 0;
    margin-left: auto;
  }

  .nav {
    padding-right: 0;
  }

  // RTL text direction for form elements
  .form-control,
  .form-select {
    text-align: right;
  }
}

// Extend utility classes for RTL layouts
.ms-auto-rtl {
  html[lang="ar"] & {
    margin-right: auto !important;
    margin-left: 0 !important;
  }
}

.me-auto-rtl {
  html[lang="ar"] & {
    margin-left: auto !important;
    margin-right: 0 !important;
  }
}

// Float utilities for RTL
html[lang="ar"] {
  .float-start {
    float: right !important;
  }

  .float-end {
    float: left !important;
  }
}

// Responsive RTL classes
@media (min-width: 768px) {
  html[lang="ar"] {
    .text-md-start {
      text-align: right !important;
    }

    .text-md-end {
      text-align: left !important;
    }
  }
}

/* Leaflet PNG path fix */
.leaflet-default-icon-path {
  background-image: url(assets/leaflet/marker-icon.png);
}

.leaflet-control-layers-toggle {
  background-image: url(assets/leaflet/layers.png);
  width: 36px;
  height: 36px;
}

.leaflet-retina .leaflet-control-layers-toggle {
  background-image: url(assets/leaflet/layers-2x.png);
  background-size: 26px 26px;
}

.leaflet-marker-icon,
.leaflet-marker-shadow {
  background-image: none !important;
}

/* Global Avatar Circular Styling */
c-avatar {
  &.rounded-circle,
  &[shape="rounded-circle"] {
    border-radius: 50% !important;
    overflow: hidden;

    img, .avatar-img {
      border-radius: 50% !important;
      object-fit: cover;
      width: 100%;
      height: 100%;
    }

    svg {
      border-radius: 50% !important;
    }
  }
}

/* User Avatar Component Styling */
app-user-avatar {
  c-avatar {
    border-radius: 50% !important;
    overflow: hidden;

    img, .avatar-img {
      border-radius: 50% !important;
      object-fit: cover;
      width: 100%;
      height: 100%;
    }

    svg {
      border-radius: 50% !important;
    }
  }
}

/* Force circular shape for all avatar images */
.avatar-img,
c-avatar img,
app-user-avatar img,
.profile-avatar-large img {
  border-radius: 50% !important;
  object-fit: cover !important;
  width: 100% !important;
  height: 100% !important;
}

/* Global Dark Mode Text Fixes */
.dark-theme,
[data-coreui-theme="dark"] {
  color: #ffffff !important;

  h1, h2, h3, h4, h5, h6,
  p, span, div, label,
  .text-muted, .text-secondary,
  td, th, tr {
    color: #ffffff !important;
  }

  .form-control, .form-select {
    color: #ffffff !important;

    &::placeholder {
      color: #ffffff !important;
    }
  }
}
