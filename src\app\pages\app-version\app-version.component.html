<!-- App Version Page Container -->
<div class="app-version-page">

  <!-- Breadcrumb Navigation -->
  <c-breadcrumb class="mb-4">
    <c-breadcrumb-item
      *ngFor="let item of breadcrumbItems"
      [active]="item.active">
      <a *ngIf="!item.active" [routerLink]="item.url">{{ item.label | translate | async }}</a>
      <span *ngIf="item.active">{{ item.label | translate | async }}</span>
    </c-breadcrumb-item>
  </c-breadcrumb>

  <!-- Page Header -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h2 class="mb-1">{{ 'appVersion.title' | translate | async }}</h2>
      <p class="text-body-secondary mb-0">
        {{ 'appVersion.subtitle' | translate | async }}
      </p>
    </div>
    <div class="d-flex gap-2">
   <!--   <button
        cButton
        color="warning"
        variant="outline"
        (click)="openForceUpdateModal()">
        <i class="bi bi-arrow-clockwise me-1"></i>
        {{ 'appVersion.forceUpdate' | translate | async }}
      </button> -->
      <button
        cButton
        color="primary"
        (click)="openAddModal()">
        <i class="bi bi-plus-lg me-1"></i>
        {{ 'appVersion.addVersion' | translate | async }}
      </button>
    </div>
  </div>

  <!-- Success Alert -->
  <c-alert
    *ngIf="successMessage"
    color="success"
    [dismissible]="true"
    (dismissed)="dismissSuccess()">
    <i class="bi bi-check-circle me-2"></i>
    {{ successMessage | translate | async }}
  </c-alert>

  <!-- Error Alert -->
  <c-alert
    *ngIf="errorMessage"
    color="danger"
    [dismissible]="true"
    (dismissed)="dismissError()">
    <i class="bi bi-exclamation-triangle me-2"></i>
    {{ errorMessage | translate | async }}
  </c-alert>

  <!-- Search and Filters -->
  <c-card class="mb-4 filter-card">
    <c-card-header class="filter-header">
      <div class="d-flex align-items-center justify-content-between w-100">
        <div class="d-flex align-items-center">
          <div class="filter-icon-wrapper me-3">
            <i class="bi bi-funnel-fill"></i>
          </div>
          <div>
            <h5 class="mb-0 filter-title">{{ 'common.search' | translate | async }}</h5>
            <p class="mb-0 filter-subtitle">{{ 'appVersion.filterSubtitle' | translate | async }}</p>
          </div>
        </div>
        <button
          type="button"
          cButton
          size="sm"
          color="light"
          variant="ghost"
          (click)="clearSearch()"
          class="clear-all-btn">
          <i class="bi bi-arrow-clockwise me-1"></i>
          {{ 'common.refresh' | translate | async }}
        </button>
      </div>
    </c-card-header>
    <c-card-body class="filter-body">
      <form [formGroup]="searchForm" (ngSubmit)="onSearch()">
        <div class="row g-4">
          <!-- Version Filter -->
          <div class="col-lg-4 col-md-6">
            <div class="filter-group">
              <label cLabel for="searchVersion" class="filter-label">
                <i class="bi bi-tag me-2"></i>
                {{ 'appVersion.version' | translate | async }}
              </label>
              <div class="input-group">
                <span class="input-group-text">
                  <i class="bi bi-hash"></i>
                </span>
                <input
                  cFormControl
                  id="searchVersion"
                  type="text"
                  formControlName="version"
                  class="form-control filter-input"
                  [placeholder]="'appVersion.versionPlaceholder' | translate | async">
                <button
                  *ngIf="searchForm.get('version')?.value"
                  type="button"
                  class="btn btn-outline-secondary btn-clear-field"
                  (click)="clearField('version')">
                  <i class="bi bi-x"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- Platform Filter -->
          <div class="col-lg-4 col-md-6">
            <div class="filter-group">
              <label cLabel for="searchPlatform" class="filter-label">
                <i class="bi bi-device-ssd me-2"></i>
                {{ 'appVersion.platform' | translate | async }}
              </label>
              <div class="select-wrapper">
                <select
                  cFormSelect
                  id="searchPlatform"
                  formControlName="platform"
                  class="form-select filter-select">
                  <option value="">
                    <i class="bi bi-list"></i>
                    {{ 'common.all' | translate | async }}
                  </option>
                  <option *ngFor="let option of platformOptions" [value]="option.value">
                    {{ option.label }}
                  </option>
                </select>
                <i class="bi bi-chevron-down select-arrow"></i>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="col-lg-4 col-md-12">
            <div class="filter-actions">
              <label class="filter-label d-block">&nbsp;</label>
              <div class="d-flex gap-2 flex-wrap">
                <button
                  type="submit"
                  cButton
                  color="primary"
                  class="search-btn flex-fill">
                  <i class="bi bi-search me-2"></i>
                  {{ 'common.search' | translate | async }}
                </button>
                <button
                  type="button"
                  cButton
                  color="light"
                  variant="outline"
                  class="clear-btn"
                  (click)="clearSearch()">
                  <i class="bi bi-x-circle me-2"></i>
                  {{ 'common.clear' | translate | async }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Filter Summary -->
        <div *ngIf="hasActiveFilters()" class="filter-summary mt-3">
          <div class="d-flex align-items-center flex-wrap gap-2">
            <span class="filter-summary-label">
              <i class="bi bi-funnel me-1"></i>
              {{ 'appVersion.activeFilters' | translate | async }}:
            </span>
            <c-badge
              *ngIf="searchForm.get('version')?.value"
              color="primary"
              class="filter-badge">
              {{ 'appVersion.version' | translate | async }}: {{ searchForm.get('version')?.value }}
              <button type="button" class="btn-close-badge" (click)="clearField('version')">
                <i class="bi bi-x"></i>
              </button>
            </c-badge>
            <c-badge
              *ngIf="searchForm.get('platform')?.value"
              color="info"
              class="filter-badge">
              {{ 'appVersion.platform' | translate | async }}: {{ getPlatformName(searchForm.get('platform')?.value) }}
              <button type="button" class="btn-close-badge" (click)="clearField('platform')">
                <i class="bi bi-x"></i>
              </button>
            </c-badge>
          </div>
        </div>
      </form>
    </c-card-body>
  </c-card>

  <!-- Data Table -->
  <c-card>
    <c-card-header>
      <h5 class="mb-0">
        <i class="bi bi-list me-2"></i>
        {{ 'appVersion.versionsList' | translate | async }}
      </h5>
    </c-card-header>
    <c-card-body>

      <!-- Loading State -->
      <div *ngIf="loading" class="text-center py-5">
        <c-spinner size="sm" class="mb-3"></c-spinner>
        <p class="text-body-secondary">{{ 'common.loading' | translate | async }}</p>
      </div>

      <!-- Data Table -->
      <div *ngIf="!loading" class="table-responsive">
        <table class="table table-striped table-hover">
          <thead class="table-dark">
            <tr>
              <th>{{ 'appVersion.version' | translate | async }}</th>
              <th>{{ 'appVersion.platform' | translate | async }}</th>
              <th>{{ 'common.createdAt' | translate | async }}</th>
              <th>{{ 'common.createdBy' | translate | async }}</th>
              <th>{{ 'common.actions' | translate | async }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let version of filteredVersions">
              <td>
                <strong>{{ version.version }}</strong>
              </td>
              <td>
                <c-badge [color]="getPlatformBadgeColor(version.platformType)">
                  {{ getPlatformName(version.platformType) }}
                </c-badge>
              </td>
              <td>{{ version.createdAt | date:'medium' }}</td>
              <td>{{ version.createdBy }}</td>
              <td>
                <div class="d-flex gap-1">
                  <button
                    cButton
                    size="sm"
                    color="info"
                    variant="outline"
                    (click)="openEditModal(version)"
                    [attr.aria-label]="'common.edit' | translate | async">
                    <i class="bi bi-pencil"></i>
                  </button>
               <!--   <button
                    cButton
                    size="sm"
                    color="danger"
                    variant="outline"
                    (click)="openDeleteModal(version)"
                    [attr.aria-label]="'common.delete' | translate | async">
                    <i class="bi bi-trash"></i>
                  </button> -->
                </div>
              </td>
            </tr>
            <tr *ngIf="filteredVersions.length === 0">
              <td colspan="5" class="text-center py-4">
                <i class="bi bi-inbox text-muted fs-1 d-block mb-2"></i>
                <p class="text-muted mb-0">{{ 'common.noDataFound' | translate | async }}</p>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

    </c-card-body>
  </c-card>

</div>

<!-- Add Version Modal -->
<c-modal
  [visible]="showAddModal"
  (visibleChange)="showAddModal = $event"
  backdrop="static">
  <c-modal-header>
    <h4 cModalTitle>
      <i class="bi bi-plus-circle me-2"></i>
      {{ 'appVersion.addVersion' | translate | async }}
    </h4>
  </c-modal-header>
  <c-modal-body>
    <form [formGroup]="addForm" (ngSubmit)="onAddVersion()">

      <!-- Version Field -->
      <div class="mb-3">
        <label cLabel for="addVersion">{{ 'appVersion.version' | translate | async }} *</label>
        <input
          cFormControl
          id="addVersion"
          type="text"
          formControlName="version"
          [class.is-invalid]="getFieldError(addForm, 'version')"
          [placeholder]="'appVersion.versionPlaceholder' | translate | async">
        <div *ngIf="getFieldError(addForm, 'version')" class="invalid-feedback">
          {{ getFieldError(addForm, 'version')! | translate | async }}
        </div>
        <div class="form-text">
          {{ 'appVersion.versionFormat' | translate | async }}
        </div>
      </div>

      <!-- Platform Field -->
      <div class="mb-3">
        <label cLabel for="addPlatform">{{ 'appVersion.platform' | translate | async }} *</label>
        <select
          cFormSelect
          id="addPlatform"
          formControlName="platform"
          [class.is-invalid]="getFieldError(addForm, 'platform')">
          <option value="">{{ 'appVersion.selectPlatform' | translate | async }}</option>
          <option *ngFor="let option of platformOptions" [value]="option.value">
            {{ option.label }}
          </option>
        </select>
        <div *ngIf="getFieldError(addForm, 'platform')" class="invalid-feedback">
          {{ getFieldError(addForm, 'platform')! | translate | async }}
        </div>
      </div>

    </form>
  </c-modal-body>
  <c-modal-footer>
    <button
      cButton
      color="secondary"
      variant="outline"
      (click)="closeAddModal()"
      [disabled]="saving">
      {{ 'common.cancel' | translate | async }}
    </button>
    <button
      cButton
      color="primary"
      (click)="onAddVersion()"
      [disabled]="!addForm.valid || saving">
      <c-spinner *ngIf="saving" size="sm" class="me-1"></c-spinner>
      <i *ngIf="!saving" class="bi bi-check me-1"></i>
      {{ 'common.save' | translate | async }}
    </button>
  </c-modal-footer>
</c-modal>

<!-- Edit Version Modal -->
<c-modal
  [visible]="showEditModal"
  (visibleChange)="showEditModal = $event"
  backdrop="static">
  <c-modal-header>
    <h4 cModalTitle>
      <i class="bi bi-pencil me-2"></i>
      {{ 'appVersion.editVersion' | translate | async }}
    </h4>
  </c-modal-header>
  <c-modal-body>
    <form [formGroup]="editForm" (ngSubmit)="onEditVersion()">

      <!-- Version Field -->
      <div class="mb-3">
        <label cLabel for="editVersion">{{ 'appVersion.version' | translate | async }} *</label>
        <input
          cFormControl
          id="editVersion"
          type="text"
          formControlName="version"
          [class.is-invalid]="getFieldError(editForm, 'version')"
          [placeholder]="'appVersion.versionPlaceholder' | translate | async">
        <div *ngIf="getFieldError(editForm, 'version')" class="invalid-feedback">
          {{ getFieldError(editForm, 'version')! | translate | async }}
        </div>
      </div>

      <!-- Platform Field -->
      <div class="mb-3">
        <label cLabel for="editPlatform">{{ 'appVersion.platform' | translate | async }} *</label>
        <select
          cFormSelect
          id="editPlatform"
          formControlName="platform"
          [class.is-invalid]="getFieldError(editForm, 'platform')">
          <option value="">{{ 'appVersion.selectPlatform' | translate | async }}</option>
          <option *ngFor="let option of platformOptions" [value]="option.value">
            {{ option.label }}
          </option>
        </select>
        <div *ngIf="getFieldError(editForm, 'platform')" class="invalid-feedback">
          {{ getFieldError(editForm, 'platform')! | translate | async }}
        </div>
      </div>

    </form>
  </c-modal-body>
  <c-modal-footer>
    <button
      cButton
      color="secondary"
      variant="outline"
      (click)="closeEditModal()"
      [disabled]="saving">
      {{ 'common.cancel' | translate | async }}
    </button>
    <button
      cButton
      color="primary"
      (click)="onEditVersion()"
      [disabled]="!editForm.valid || saving">
      <c-spinner *ngIf="saving" size="sm" class="me-1"></c-spinner>
      <i *ngIf="!saving" class="bi bi-check me-1"></i>
      {{ 'common.update' | translate | async }}
    </button>
  </c-modal-footer>
</c-modal>

<!-- Delete Confirmation Modal -->
<c-modal
  [visible]="showDeleteModal"
  (visibleChange)="showDeleteModal = $event"
  backdrop="static">
  <c-modal-header>
    <h4 cModalTitle>
      <i class="bi bi-exclamation-triangle me-2 text-danger"></i>
      {{ 'common.confirmDelete' | translate | async }}
    </h4>
  </c-modal-header>
  <c-modal-body>
    <p>{{ 'appVersion.deleteConfirmation' | translate | async }}</p>
    <div *ngIf="selectedVersion" class="alert alert-warning">
      <strong>{{ 'appVersion.version' | translate | async }}:</strong> {{ selectedVersion.version }}<br>
      <strong>{{ 'appVersion.platform' | translate | async }}:</strong> {{ getPlatformName(selectedVersion.platformType) }}
    </div>
  </c-modal-body>
  <c-modal-footer>
    <button
      cButton
      color="secondary"
      variant="outline"
      (click)="closeDeleteModal()"
      [disabled]="saving">
      {{ 'common.cancel' | translate | async }}
    </button>
    <button
      cButton
      color="danger"
      (click)="onDeleteVersion()"
      [disabled]="saving">
      <c-spinner *ngIf="saving" size="sm" class="me-1"></c-spinner>
      <i *ngIf="!saving" class="bi bi-trash me-1"></i>
      {{ 'common.delete' | translate | async }}
    </button>
  </c-modal-footer>
</c-modal>

<!-- Force Update Modal -->
<c-modal
  [visible]="showForceUpdateModal"
  (visibleChange)="showForceUpdateModal = $event"
  backdrop="static">
  <c-modal-header>
    <h4 cModalTitle>
      <i class="bi bi-arrow-clockwise me-2 text-warning"></i>
      {{ 'appVersion.forceUpdate' | translate | async }}
    </h4>
  </c-modal-header>
  <c-modal-body>
    <div class="alert alert-warning">
      <i class="bi bi-exclamation-triangle me-2"></i>
      {{ 'appVersion.forceUpdateWarning' | translate | async }}
    </div>

    <form [formGroup]="forceUpdateForm" (ngSubmit)="onForceUpdate()">

      <!-- Version Field -->
      <div class="mb-3">
        <label cLabel for="forceUpdateVersion">{{ 'appVersion.version' | translate | async }} *</label>
        <input
          cFormControl
          id="forceUpdateVersion"
          type="text"
          formControlName="version"
          [class.is-invalid]="getFieldError(forceUpdateForm, 'version')"
          [placeholder]="'appVersion.versionPlaceholder' | translate | async">
        <div *ngIf="getFieldError(forceUpdateForm, 'version')" class="invalid-feedback">
          {{ getFieldError(forceUpdateForm, 'version')! | translate | async }}
        </div>
      </div>

      <!-- Platform Field -->
      <div class="mb-3">
        <label cLabel for="forceUpdatePlatform">{{ 'appVersion.platform' | translate | async }} *</label>
        <select
          cFormSelect
          id="forceUpdatePlatform"
          formControlName="platform"
          [class.is-invalid]="getFieldError(forceUpdateForm, 'platform')">
          <option value="">{{ 'appVersion.selectPlatform' | translate | async }}</option>
          <option *ngFor="let option of platformOptions" [value]="option.value">
            {{ option.label }}
          </option>
        </select>
        <div *ngIf="getFieldError(forceUpdateForm, 'platform')" class="invalid-feedback">
          {{ getFieldError(forceUpdateForm, 'platform')! | translate | async }}
        </div>
      </div>

    </form>
  </c-modal-body>
  <c-modal-footer>
    <button
      cButton
      color="secondary"
      variant="outline"
      (click)="closeForceUpdateModal()"
      [disabled]="saving">
      {{ 'common.cancel' | translate | async }}
    </button>
    <button
      cButton
      color="warning"
      (click)="onForceUpdate()"
      [disabled]="!forceUpdateForm.valid || saving">
      <c-spinner *ngIf="saving" size="sm" class="me-1"></c-spinner>
      <i *ngIf="!saving" class="bi bi-arrow-clockwise me-1"></i>
      {{ 'appVersion.forceUpdate' | translate | async }}
    </button>
  </c-modal-footer>
</c-modal>
