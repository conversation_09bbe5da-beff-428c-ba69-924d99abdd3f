:host {
  .legend {
    small {
      font-size: x-small;
    }
  }
}

// General dashboard styles
:host {
  .card-body {
    overflow-x: auto;
  }
}

// Vehicle metrics styles
.vehicle-metric-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  }

  .metric-icon {
    min-width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }
}

.bg-light-success {
  background-color: rgba(77, 189, 116, 0.1);
}

.bg-light-info {
  background-color: rgba(51, 153, 255, 0.1);
}

.bg-light-warning {
  background-color: rgba(249, 177, 21, 0.1);
}

.bg-light-danger {
  background-color: rgba(229, 83, 83, 0.1);
}

.bg-light-primary {
  background-color: rgba(50, 31, 219, 0.1);
}

// Vehicle table styles
table {
  &.cTable {
    th {
      font-weight: 600;
      text-transform: uppercase;
      font-size: 0.8rem;
      letter-spacing: 0.5px;
    }

    td {
      vertical-align: middle;
    }
  }
}

// Button group styles
.btn-group {
  .btn {
    border-radius: 4px;
    margin: 0 2px;

    &.active {
      box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    }
  }
}

// Progress bar styles
.progress {
  background-color: rgba(0, 0, 0, 0.05);

  .progress-bar {
    transition: width 0.6s ease;
  }
}

// Media queries for better responsiveness
@media (max-width: 768px) {
  .vehicle-metric-card {
    margin-bottom: 1rem;
  }

  .metric-icon {
    min-width: 40px !important;
    height: 40px !important;
  }
}

// Quick action buttons styling
.action-button-card {
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(0, 0, 0, 0.05);

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  }

  .action-icon {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.1);
    }

    svg {
      transition: all 0.3s ease;
    }
  }

  h6 {
    margin-top: 10px;
    transition: all 0.3s ease;
  }

  &:hover h6 {
    color: var(--cui-primary);
  }
}

// Responsive styles for action buttons
@media (max-width: 576px) {
  .action-button-card {
    .action-icon {
      width: 50px;
      height: 50px;
    }

    h6 {
      font-size: 0.85rem;
    }
  }
}

// Tab navigation styling
c-nav {
  &.nav-tabs {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);

    .nav-item {
      margin-bottom: -1px;

      a {
        padding: 0.75rem 1.25rem;
        font-weight: 500;
        color: var(--cui-body-color);
        border: none;
        border-bottom: 2px solid transparent;
        border-radius: 0;
        transition: all 0.3s ease;
        position: relative;

        &:hover {
          background-color: rgba(0, 0, 0, 0.02);
          border-color: rgba(0, 0, 0, 0.1);
        }

        &.active {
          color: var(--cui-primary);
          background: transparent;
          border-bottom: 2px solid var(--cui-primary);

          &::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 10px;
            height: 10px;
            background-color: var(--cui-primary);
            border-radius: 50%;
          }
        }
      }
    }
  }
}

// Chart container styles
c-tab-content {
  padding: 1.5rem 0;

  c-tab-pane {
    transition: opacity 0.3s ease;

    &:not(.active) {
      display: none;
    }
  }
}
