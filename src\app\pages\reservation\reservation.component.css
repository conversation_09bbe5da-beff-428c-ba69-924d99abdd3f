.page-title {
  color: var(--bs-dark, #212529);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.page-title i {
  color: var(--bs-primary, #0d6efd);
}

.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;
}

.card-header {
  background-color: var(--bs-light, #f8f9fa);
  border-bottom: 1px solid var(--bs-border-color, #dee2e6);
  border-radius: 0.5rem 0.5rem 0 0 !important;
}

.table {
  margin-bottom: 0;
}

.table th {
  background-color: var(--bs-light, #f8f9fa);
  border-top: none;
  font-weight: 600;
  color: var(--bs-dark, #212529);
  padding: 1rem 0.75rem;
}

.table td {
  padding: 1rem 0.75rem;
  vertical-align: middle;
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
  background-color: rgba(0, 0, 0, 0.02);
}

.badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
}

.badge.bg-primary {
  background-color: var(--bs-primary, #0d6efd) !important;
}

.badge.bg-success {
  background-color: var(--bs-success, #198754) !important;
}

.badge.bg-warning {
  background-color: var(--bs-warning, #ffc107) !important;
  color: var(--bs-dark, #212529) !important;
}

.badge.bg-danger {
  background-color: var(--bs-danger, #dc3545) !important;
}

.badge.bg-secondary {
  background-color: var(--bs-secondary, #6c757d) !important;
}

.reservation-details-modal .modal-content {
  border: none;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.modal-header-professional {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 2rem;
  border-bottom: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.header-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.modal-title-main {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.status-badge-large {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.9) !important;
  color: #333 !important;
}

.modal-body-professional {
  padding: 2rem;
  background: #f8fafc;
}

.info-section {
  background: white;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-header {
  background: #f1f5f9;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.section-icon {
  width: 32px;
  height: 32px;
  background: #667eea;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.section-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #334155;
}

.section-content {
  padding: 1.5rem;
}

.info-item {
  margin-bottom: 1rem;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.info-value {
  font-size: 1rem;
  font-weight: 500;
  color: #1e293b;
  padding: 0.5rem 0.75rem;
  background: #f8fafc;
  border-radius: 8px;
}

.modal-footer-professional {
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  padding: 1.5rem 2rem;
}

.footer-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.btn-close-modal,
.btn-print-modal {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.btn-group .btn {
  border-radius: 0.25rem;
  margin-right: 0.25rem;
  transition: all 0.15s ease-in-out;
}

.btn-group .btn:last-child {
  margin-right: 0;
}

.btn-group .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  margin-bottom: 24px;
  background: #f8fafc;
  padding: 18px 20px;
  border-radius: 12px;
}

.filter-form label {
  font-weight: 500;
  color: #334155;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-form input,
.filter-form select {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background-color: white;
  color: #1e293b;
  min-width: 150px;
}

.filter-form input:focus,
.filter-form select:focus {
  border-color: #0ea5e9;
  outline: none;
}

[data-coreui-theme="dark"] .filter-form {
  background: #374151;
}

[data-coreui-theme="dark"] .filter-form label {
  color: #e5e7eb;
}

[data-coreui-theme="dark"] .filter-form input,
[data-coreui-theme="dark"] .filter-form select {
  background-color: #374151;
  border-color: #6b7280;
  color: #f9fafb;
}

.loading-overlay {
  position: relative;
}

.loading-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.no-data {
  text-align: center;
  padding: 3rem 1rem;
}

.no-data i {
  color: var(--bs-muted, #6c757d);
  margin-bottom: 1rem;
}
.modal-header {
  border-bottom: 1px solid var(--bs-border-color, #dee2e6);
  background-color: var(--bs-light, #f8f9fa);
}

.modal-footer {
  border-top: 1px solid var(--bs-border-color, #dee2e6);
  background-color: var(--bs-light, #f8f9fa);
}

.form-control-plaintext {
  padding: 0.375rem 0;
  margin-bottom: 0;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--bs-body-color, #212529);
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0;
}

.form-label.fw-bold {
  font-weight: 600;
  color: var(--bs-dark, #212529);
  margin-bottom: 0.25rem;
}

@media (max-width: 800px) {
  .filter-form {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .filter-form label {
    width: 100%;
    justify-content: space-between;
  }

  .filter-form input[type="text"],
  .filter-form input[type="date"],
  .filter-form select {
    min-width: auto;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .btn-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .btn-group .btn {
    margin-right: 0;
    margin-bottom: 0.25rem;
  }

  .table-responsive {
    font-size: 0.875rem;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .filter-form {
    padding: 14px 16px;
    margin-bottom: 20px;
  }
}

@media (max-width: 576px) {
  .card-body {
    padding: 1rem;
  }

  .card-header h5 {
    font-size: 1rem;
  }

  .page-title {
    font-size: 1.25rem;
  }

  .filter-form {
    padding: 12px;
    gap: 8px;
  }

  .filter-form label {
    font-size: 0.9rem;
  }

  .filter-form input[type="text"],
  .filter-form input[type="date"],
  .filter-form select {
    font-size: 0.9rem;
    padding: 6px 10px;
  }
}

/* Dark Mode Support */
[data-coreui-theme="dark"] .page-title {
  color: var(--bs-light, #f8f9fa);
}

[data-coreui-theme="dark"] .card {
  background-color: var(--bs-dark, #212529);
  border-color: var(--bs-gray-600, #6c757d);
}

[data-coreui-theme="dark"] .card-header {
  background-color: var(--bs-gray-800, #343a40);
  border-color: var(--bs-gray-600, #6c757d);
}

[data-coreui-theme="dark"] .table th {
  background-color: var(--bs-gray-800, #343a40);
  color: var(--bs-light, #f8f9fa);
  border-color: var(--bs-gray-600, #6c757d);
}

[data-coreui-theme="dark"] .table td {
  color: var(--bs-light, #f8f9fa);
  border-color: var(--bs-gray-600, #6c757d);
}

[data-coreui-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > td {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-coreui-theme="dark"] .form-control-plaintext {
  color: var(--bs-light, #f8f9fa);
}

[data-coreui-theme="dark"] .form-label.fw-bold {
  color: var(--bs-light, #f8f9fa);
}

[data-coreui-theme="dark"] .modal-header,
[data-coreui-theme="dark"] .modal-footer {
  background-color: var(--bs-gray-800, #343a40);
  border-color: var(--bs-gray-600, #6c757d);
}

/* Dark Mode Support for Filter Form */
[data-coreui-theme="dark"] .card-body form .form-label {
  color: var(--bs-light, #f8f9fa);
}

[data-coreui-theme="dark"] .card-body form .form-control,
[data-coreui-theme="dark"] .card-body form .form-select {
  background-color: var(--bs-gray-800, #343a40);
  border-color: var(--bs-gray-600, #6c757d);
  color: var(--bs-light, #f8f9fa);
}

[data-coreui-theme="dark"] .card-body form .form-control:focus,
[data-coreui-theme="dark"] .card-body form .form-select:focus {
  background-color: var(--bs-gray-800, #343a40);
  border-color: var(--bs-primary, #0d6efd);
  color: var(--bs-light, #f8f9fa);
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

[data-coreui-theme="dark"] .card-body form .form-control::placeholder {
  color: var(--bs-gray-400, #adb5bd);
}

/* ===== DARK MODE SUPPORT FOR PROFESSIONAL MODAL ===== */
[data-coreui-theme="dark"] .reservation-details-modal .modal-content {
  background: var(--bs-gray-800, #343a40);
  color: var(--bs-light, #f8f9fa);
}

[data-coreui-theme="dark"] .modal-body-professional {
  background: var(--bs-gray-900, #212529);
}

[data-coreui-theme="dark"] .info-section {
  background: var(--bs-gray-800, #343a40);
  border: 1px solid var(--bs-gray-600, #6c757d);
}

[data-coreui-theme="dark"] .section-header {
  background: linear-gradient(135deg, var(--bs-gray-700, #495057) 0%, var(--bs-gray-600, #6c757d) 100%);
  border-color: var(--bs-gray-600, #6c757d);
}

[data-coreui-theme="dark"] .section-title {
  color: var(--bs-light, #f8f9fa);
}

[data-coreui-theme="dark"] .info-label {
  color: var(--bs-gray-300, #adb5bd);
}

[data-coreui-theme="dark"] .info-value {
  background: var(--bs-gray-700, #495057);
  color: var(--bs-light, #f8f9fa);
  border-left-color: var(--bs-gray-600, #6c757d);
}

[data-coreui-theme="dark"] .modal-footer-professional {
  background: var(--bs-gray-800, #343a40);
  border-color: var(--bs-gray-600, #6c757d);
}

/* Animation */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom Scrollbar */
.table-responsive::-webkit-scrollbar {
  height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
  background: var(--bs-light, #f8f9fa);
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: var(--bs-secondary, #6c757d);
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: var(--bs-dark, #212529);
}
