<c-row ngPreserveWhitespaces>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Button Group</strong> <span>Basic example</span>
      </c-card-header>
      <c-card-body>
        <p>
          Wrap a series of <code>&lt;CButton&gt;</code> components in
          <code>&lt;c-button-group&gt;</code>.
        </p>
        <app-docs-example href="components/button-group">
          <c-button-group aria-label="Basic example" role="group">
            <button cButton color="primary">Left</button>
            <button cButton color="primary">Middle</button>
            <button cButton color="primary">Right</button>
          </c-button-group>
        </app-docs-example>
        <p>
          These classes can also be added to groups of links, as an alternative to the
          <code>&lt;CNav&gt;</code> components.
        </p>
        <app-docs-example href="components/button-group">
          <c-button-group>
            <a [active]="true" cButton color="primary" [routerLink]="[]">
              Active link
            </a>
            <a cButton color="primary" [routerLink]="[]">
              Link
            </a>
            <a cButton color="primary" [routerLink]="[]">
              Link
            </a>
          </c-button-group>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Button Group</strong> <span>Mixed styles</span>
      </c-card-header>
      <c-card-body>
        <app-docs-example href="components/button-group#mixed-styles">
          <c-button-group aria-label="Basic mixed styles example" role="group">
            <button cButton color="danger">Left</button>
            <button cButton color="warning">Middle</button>
            <button cButton color="success">Right</button>
          </c-button-group>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Button Group</strong> <span>Outlined styles</span>
      </c-card-header>
      <c-card-body>
        <app-docs-example href="components/button-group#outlined-styles">
          <c-button-group aria-label="Basic outlined example" role="group">
            <button cButton color="primary" variant="outline">
              Left
            </button>
            <button cButton color="primary" variant="outline">
              Middle
            </button>
            <button cButton color="primary" variant="outline">
              Right
            </button>
          </c-button-group>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Button Group</strong> <span>Checkbox and radio button groups</span>
      </c-card-header>
      <c-card-body>
        <p>
          Combine button-like checkbox and radio toggle buttons into a seamless looking button
          group.
        </p>
        <app-docs-example href="components/button-group#checkbox-and-radio-button-groups">
          <form [formGroup]="formCheck1">
            <c-button-group aria-label="Basic checkbox toggle button group" role="group">
              <input class="btn-check" formControlName="checkbox1" type="checkbox"/>
              <label (click)="setCheckBoxValue('checkbox1')" cButton cFormCheckLabel
                     variant="outline">Checkbox 1</label>

              <input class="btn-check" formControlName="checkbox2" type="checkbox"/>
              <label (click)="setCheckBoxValue('checkbox2')" cButton cFormCheckLabel
                     variant="outline">Checkbox 2</label>

              <input class="btn-check" formControlName="checkbox3" type="checkbox"/>
              <label (click)="setCheckBoxValue('checkbox3')" cButton cFormCheckLabel variant="outline">Checkbox
                3</label>
            </c-button-group>
          </form>
          <br class="mb-3">
        </app-docs-example>
        <app-docs-example href="components/button-group#checkbox-and-radio-button-groups">
          <form [formGroup]="formRadio1">
            <c-button-group aria-label="Basic radio toggle button group" role="group">
              <input class="btn-check" formControlName="radio1" type="radio" value="Radio1"/>
              <label (click)="setRadioValue('Radio1')" cButton cFormCheckLabel
                     variant="outline">Radio 1</label>

              <input class="btn-check" formControlName="radio1" type="radio" value="Radio2"/>
              <label (click)="setRadioValue('Radio2')" cButton cFormCheckLabel
                     variant="outline">Radio 2</label>

              <input class="btn-check" formControlName="radio1" type="radio" value="Radio3"/>
              <label (click)="setRadioValue('Radio3')" cButton cFormCheckLabel variant="outline">Radio 3</label>
            </c-button-group>
          </form>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Button Group</strong> <span>Button toolbar</span>
      </c-card-header>
      <c-card-body>
        <p>
          Join sets of button groups into button toolbars for more complicated components. Use
          utility classes as needed to space out groups, buttons, and more.
        </p>
        <app-docs-example href="components/button-group#button-toolbar">
          <c-button-toolbar aria-label="Toolbar with button groups" role="group">
            <c-button-group aria-label="First group" class="me-2" role="group">
              <button cButton color="primary">1</button>
              <button cButton color="primary">2</button>
              <button cButton color="primary">3</button>
              <button cButton color="primary">4</button>
            </c-button-group>
            <c-button-group aria-label="Second group" class="me-2" role="group">
              <button cButton color="secondary">5</button>
              <button cButton color="secondary">6</button>
              <button cButton color="secondary">7</button>
            </c-button-group>
            <c-button-group aria-label="Third group" class="me-2" role="group">
              <button cButton color="info">8</button>
            </c-button-group>
          </c-button-toolbar>
        </app-docs-example>
        <p>
          Feel free to combine input groups with button groups in your toolbars. Similar to the
          example above, you’ll likely need some utilities through to space items correctly.
        </p>
        <app-docs-example href="components/button-group#button-toolbar">
          <c-button-toolbar aria-label="Toolbar with button groups" class="mb-3" role="group">
            <c-button-group aria-label="First group" class="me-2" role="group">
              <button cButton color="secondary" variant="outline">
                1
              </button>
              <button cButton color="secondary" variant="outline">
                2
              </button>
              <button cButton color="secondary" variant="outline">
                3
              </button>
              <button cButton color="secondary" variant="outline">
                4
              </button>
            </c-button-group>
            <c-input-group>
              <span cInputGroupText>&#64;</span>
              <input aria-describedby="btnGroupAddon"
                     aria-label="Input group example"
                     cFormControl
                     placeholder="Input group example"
              />
            </c-input-group>
          </c-button-toolbar>
          <c-button-toolbar
            aria-label="Toolbar with button groups"
            class="justify-content-between"
            role="group"
          >
            <c-button-group aria-label="First group" class="me-2" role="group">
              <button cButton color="secondary" variant="outline">
                1
              </button>
              <button cButton color="secondary" variant="outline">
                2
              </button>
              <button cButton color="secondary" variant="outline">
                3
              </button>
              <button cButton color="secondary" variant="outline">
                4
              </button>
            </c-button-group>
            <c-input-group>
              <span cInputGroupText>&#64;</span>
              <input aria-describedby="btnGroupAddon"
                     aria-label="Input group example"
                     cFormControl
                     placeholder="Input group example"
              />
            </c-input-group>
          </c-button-toolbar>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Button Group</strong> <span>Sizing</span>
      </c-card-header>
      <c-card-body>
        <p>
          Alternatively, of implementing button sizing classes to each button in a group, set
          <code>size</code> property to all <code>&lt;c-button-group&gt;</code>&#39;s, including
          each one when nesting multiple groups.
        </p>
        <app-docs-example href="components/button-group#sizing">
          <c-button-group aria-label="Large button group" class="m-1" role="group" size="lg">
            <button cButton color="dark" variant="outline">
              Left
            </button>
            <button cButton color="dark" variant="outline">
              Middle
            </button>
            <button cButton color="dark" variant="outline">
              Right
            </button>
          </c-button-group>
          <br/>
          <c-button-group aria-label="Default button group" class="m-1" role="group">
            <button cButton color="dark" variant="outline">
              Left
            </button>
            <button cButton color="dark" variant="outline">
              Middle
            </button>
            <button cButton color="dark" variant="outline">
              Right
            </button>
          </c-button-group>
          <br/>
          <c-button-group aria-label="Small button group" class="m-1" role="group" size="sm">
            <button cButton color="dark" variant="outline">
              Left
            </button>
            <button cButton color="dark" variant="outline">
              Middle
            </button>
            <button cButton color="dark" variant="outline">
              Right
            </button>
          </c-button-group>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Button Group</strong> <span>Nesting</span>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Put a <code>&lt;c-button-group&gt;</code> inside another
          <code>&lt;c-button-group&gt;</code> when you need dropdown menus combined with a series
          of buttons.
        </p>
        <app-docs-example href="components/button-group#nesting">
          <c-button-group aria-label="Button group with nested dropdown" role="group">
            <button cButton color="primary">1</button>
            <button cButton color="primary">2</button>
            <c-dropdown variant="btn-group">
              <button cButton cDropdownToggle>
                Dropdown
              </button>
              <ul cDropdownMenu>
                <li><a cDropdownItem [routerLink]="[]">Action</a></li>
                <li><a cDropdownItem [routerLink]="[]">Another action</a></li>
                <li><a cDropdownItem [routerLink]="[]">Something else here</a></li>
                <li cDropdownDivider></li>
                <li><a cDropdownItem [routerLink]="[]">Separated link</a></li>
              </ul>
            </c-dropdown>
          </c-button-group>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header>
        <strong>Angular Button Group</strong> <span>Vertical variation</span>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Create a set of buttons that appear vertically stacked rather than horizontally.
          <strong>Split button dropdowns are not supported here.</strong>
        </p>
        <app-docs-example href="components/button-group/#vertical-variation">
          <c-button-group [vertical]="true" aria-label="Vertical button group" role="group">
            <button cButton color="dark">Button</button>
            <button cButton color="dark">Button</button>
            <button cButton color="dark">Button</button>
            <button cButton color="dark">Button</button>
            <button cButton color="dark">Button</button>
            <button cButton color="dark">Button</button>
            <button cButton color="dark">Button</button>
          </c-button-group>
          <br>
        </app-docs-example>
        <app-docs-example href="components/button-group/#vertical-variation">
          <c-button-group [vertical]="true" aria-label="Vertical button group" role="group">
            <button cButton color="primary">Button</button>
            <button cButton color="primary">Button</button>
            <c-dropdown variant="btn-group">
              <button cButton cDropdownToggle>
                Dropdown
              </button>
              <ul cDropdownMenu>
                <li><a cDropdownItem [routerLink]="[]">Action</a></li>
                <li><a cDropdownItem [routerLink]="[]">Another action</a></li>
                <li><a cDropdownItem [routerLink]="[]">Something else here</a></li>
                <li cDropdownDivider></li>
                <li><a cDropdownItem [routerLink]="[]">Separated link</a></li>
              </ul>
            </c-dropdown>
            <button cButton color="primary">Button</button>
            <button cButton color="primary">Button</button>
            <c-dropdown variant="btn-group">
              <button cButton cDropdownToggle>
                Dropdown
              </button>
              <ul cDropdownMenu>
                <li><a cDropdownItem [routerLink]="[]">Action</a></li>
                <li><a cDropdownItem [routerLink]="[]">Another action</a></li>
                <li><a cDropdownItem [routerLink]="[]">Something else here</a></li>
                <li cDropdownDivider></li>
                <li><a cDropdownItem [routerLink]="[]">Separated link</a></li>
              </ul>
            </c-dropdown>
            <c-dropdown placement="right-start" variant="btn-group">
              <button cButton cDropdownToggle>
                Dropdown
              </button>
              <ul cDropdownMenu dark>
                <li><a cDropdownItem [routerLink]="[]">Action</a></li>
                <li><a cDropdownItem [routerLink]="[]">Another action</a></li>
                <li><a cDropdownItem [routerLink]="[]">Something else here</a></li>
                <li cDropdownDivider></li>
                <li><a cDropdownItem [routerLink]="[]">Separated link</a></li>
              </ul>
            </c-dropdown>
            <c-dropdown variant="btn-group">
              <button cButton cDropdownToggle>
                Dropdown
              </button>
              <ul cDropdownMenu>
                <li><a cDropdownItem [routerLink]="[]">Action</a></li>
                <li><a cDropdownItem [routerLink]="[]">Another action</a></li>
                <li><a cDropdownItem [routerLink]="[]">Something else here</a></li>
                <li cDropdownDivider></li>
                <li><a cDropdownItem [routerLink]="[]">Separated link</a></li>
              </ul>
            </c-dropdown>
          </c-button-group>
        </app-docs-example>
        <app-docs-example href="components/button-group/#vertical-variation">
          <form [formGroup]="formRadio1">
            <c-button-group [vertical]="true" aria-label="Vertical button group" role="group">
              <input class="btn-check" formControlName="radio1" type="radio" value="Radio1"/>
              <label (click)="setRadioValue('Radio1')" cButton cFormCheckLabel color="danger"
                     variant="outline">Radio 1</label>

              <input class="btn-check" formControlName="radio1" type="radio" value="Radio2"/>
              <label (click)="setRadioValue('Radio2')" cButton cFormCheckLabel color="danger"
                     variant="outline">Radio 2</label>

              <input class="btn-check" formControlName="radio1" type="radio" value="Radio3"/>
              <label (click)="setRadioValue('Radio3')" cButton cFormCheckLabel color="danger" variant="outline">Radio
                3</label>
            </c-button-group>
          </form>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>
