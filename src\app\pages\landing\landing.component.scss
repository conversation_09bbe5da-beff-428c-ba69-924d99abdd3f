 /* CTA Section */
.cta {
  padding: 4rem 0;
  background: linear-gradient(135deg, #CD2C4E, #E91E63);
  color: #fff;
  text-align: center;
}

.cta-content h2 {
  font-size: 2.2rem;
  margin-bottom: 1rem;
  color: #fff;
}

.cta-content p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.cta .app-buttons {
  justify-content: center;
  margin-bottom: 2rem;
}

.cta-features {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.cta-feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cta-feature span {
  color: #4CAF50;
  font-weight: bold;
  font-size: 1.2rem;
}

.cta-feature p {
  margin: 0;
  font-size: 1rem;
}

/* Simple Scroll Animations */
.animate-fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-slide-up {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Active state when in view */
.animate-fade-in.in-view {
  opacity: 1;
  transform: translateY(0);
}

.animate-slide-up.in-view {
  opacity: 1;
  transform: translateY(0);
}
