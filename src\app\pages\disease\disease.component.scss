.custom-card {
  overflow: hidden;
  transition: all 0.3s ease;

  .card-header {
    border-bottom: 0;

    &.bg-primary {
      background: var(--cui-primary);
    }

    &.bg-info {
      background: var(--cui-info);
    }
  }
}// Table styling
.table {
  th {
    font-weight: 600;
    color: var(--cui-body-color);
    background-color: var(--cui-tertiary-bg);
    border-bottom-width: 1px;

    &.text-end {
      padding-left: 8px;
      padding-right: 16px;
    }

    &.text-start {
      padding-left: 16px;
      padding-right: 8px;
    }
  }

  td {
    vertical-align: middle;

    &.text-end {
      padding-left: 8px;
      padding-right: 16px;
      font-family: 'Cairo', sans-serif; // خط مناسب للغة العربية
    }

    &.text-start {
      padding-left: 16px;
      padding-right: 8px;
      font-family: 'Roboto', sans-serif; // خط مناسب للغة الإنجليزية
    }
  }

  // تحسين العرض على الهواتف
  @media (max-width: 768px) {
    th, td {
      font-size: 0.875rem; // حجم خط أصغر على الشاشات الصغيرة

      &:first-child {
        padding-left: 8px;
      }

      &:last-child {
        padding-right: 8px;
      }
    }

    .btn-group .btn {
      padding: 0.2rem 0.4rem;
    }
  }
}

// Button group styling
.btn-group {
  .btn {
    padding: 0.25rem 0.5rem;
    margin: 0 2px;

    &:hover {
      transform: translateY(-1px);
      transition: all 0.2s ease;
    }
  }
}

// RTL Text alignment
.text-end {
  text-align: end;
}

// Responsive Search
@media (max-width: 768px) {
  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .d-flex.gap-2 {
    flex-direction: column;
    width: 100%;
  }

  form.d-flex {
    width: 100%;

    .input-group {
      width: 100%;
    }
  }

  button.btn-icon-text {
    width: 100%;
  }
}

// Form styling
.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-control {
  &:focus {
    box-shadow: 0 0 0 0.25rem rgba(205, 44, 78, 0.25);
    border-color: var(--cui-primary);
  }
}

// Card styling enhancements
.card {
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);

  .card-header {
    background-color: var(--cui-card-cap-bg);
    border-bottom: 1px solid var(--cui-card-border-color);
    padding: 1rem;

    h4 {
      margin-bottom: 0;
      font-weight: 600;

      svg {
        vertical-align: middle;
      }
    }
  }

  .card-body {
    padding: 1.5rem;
  }
}

// Animation for the spinner
@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

.spinner-border {
  border-width: 0.15rem;
}

// تحسين مظهر الجدول
table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 0.5rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  thead tr {
    background-color: #f8f9fa;

    th {
      padding: 0.75rem;
      font-weight: 600;
      letter-spacing: 0.03em;
      text-transform: uppercase;
      font-size: 0.75rem;
      border-bottom: 2px solid #dee2e6;
    }
  }

  tbody {
    tr {
      &:hover {
        background-color: rgba(205, 44, 78, 0.05) !important;
      }

      td {
        padding: 0.75rem;
        border-top: 1px solid #dee2e6;

        // تأثير العمود الأول
        &:first-child {
          font-weight: 600;
        }
      }
    }
  }
}

// تعزيز خصائص الجدول المخططة وعند المرور بالفأرة
[cTable][hover] tbody tr:hover {
  background-color: rgba(205, 44, 78, 0.1) !important;
}

[cTable][striped] tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.02);
}

// أزرار الإجراءات
.btn-group .btn {
  position: relative;
  overflow: hidden;

  &:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
  }

  &:focus:not(:active)::after {
    animation: ripple 1s ease-out;
  }
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  20% {
    transform: scale(25, 25);
    opacity: 0.5;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}

// أنماط المدخلات العربية
.arabic-input {
  font-family: 'Cairo', sans-serif;
  font-weight: 500;
  text-align: right;

  &::placeholder {
    text-align: right;
    font-style: normal;
    opacity: 0.7;
  }

  &:focus {
    box-shadow: 0 0 0 0.25rem rgba(205, 44, 78, 0.25);
    border-color: var(--cui-primary);
  }
}

// عندما يكون النموذج في حالة تعديل
form {
  max-width: 800px;
  margin: 0 auto;

  .form-control {
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

    &:focus {
      box-shadow: 0 0 0 0.25rem rgba(205, 44, 78, 0.25);
      border-color: var(--cui-primary);
    }
  }
}

// Button hover effect styling
.btn-hover-effect {
  transition: all 0.3s ease;
  border: none;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}
