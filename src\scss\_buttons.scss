/* Professional <PERSON><PERSON> Styles for the entire application */

/* Base Button Styles */
.btn {
  border-radius: 8px;
  padding: 0.5rem 1.25rem;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  border: none;
  position: relative;
  overflow: hidden;
  text-transform: none;
  letter-spacing: 0.02em;

  &:focus {
    box-shadow: 0 0 0 3px rgba(var(--cui-primary-rgb), 0.25);
  }

  &:active {
    transform: translateY(1px);
  }
}

/* Primary Button */
.btn-primary {
  background: var(--cui-primary);
  color: white;
  border: none;

  &:hover, &:focus {
    background: var(--cui-primary-dark, #0954a5);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.12);
  }

  &:active {
    background: var(--cui-primary-dark, #0954a5);
  }
}

/* Secondary Button */
.btn-secondary {
  background: var(--cui-secondary);
  color: white;

  &:hover, &:focus {
    background: var(--cui-secondary-dark, #5c636a);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.12);
  }

  &:active {
    background: var(--cui-secondary-dark, #5c636a);
  }
}

/* Success Button */
.btn-success {
  background: var(--cui-success);
  color: white;

  &:hover, &:focus {
    background: var(--cui-success-dark, #1a7d4b);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.12);
  }

  &:active {
    background: var(--cui-success-dark, #1a7d4b);
  }
}

/* Danger Button */
.btn-danger {
  background: var(--cui-danger);
  color: white;

  &:hover, &:focus {
    background: var(--cui-danger-dark, #b82b34);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.12);
  }

  &:active {
    background: var(--cui-danger-dark, #b82b34);
  }
}

/* Warning Button */
.btn-warning {
  background: var(--cui-warning);
  color: var(--cui-body-color);

  &:hover, &:focus {
    background: var(--cui-warning-dark, #cc9e00);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.12);
  }

  &:active {
    background: var(--cui-warning-dark, #cc9e00);
  }
}

/* Info Button */
.btn-info {
  background: var(--cui-info);
  color: white;

  &:hover, &:focus {
    background: var(--cui-info-dark, #0c85bb);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.12);
  }

  &:active {
    background: var(--cui-info-dark, #0c85bb);
  }
}

/* Light Button */
.btn-light {
  background: var(--cui-light);
  color: var(--cui-body-color);

  &:hover, &:focus {
    background: var(--cui-light-dark, #ebebeb);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.08);
  }

  &:active {
    background: var(--cui-light-dark, #ebebeb);
  }
}

/* Dark Button */
.btn-dark {
  background: var(--cui-dark);
  color: white;

  &:hover, &:focus {
    background: var(--cui-dark-dark, #141619);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);
  }

  &:active {
    background: var(--cui-dark-dark, #141619);
  }
}

/* Outline Buttons */
.btn-outline-primary,
.btn-outline-secondary,
.btn-outline-success,
.btn-outline-danger,
.btn-outline-warning,
.btn-outline-info,
.btn-outline-light,
.btn-outline-dark {
  background: transparent;
  box-shadow: none;

  &:hover, &:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.08);
  }

  &:active {
    transform: translateY(1px);
    box-shadow: none;
  }
}

/* Button Sizes */
.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 6px;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.1rem;
  border-radius: 10px;
}

/* Button Group Styling */
.btn-group {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  border-radius: 8px;

  .btn {
    box-shadow: none;
  }
}

/* Button with Icon */
.btn-icon-text {
  display: inline-flex;
  align-items: center;

  i, svg {
    margin-right: 0.5rem;
  }
}

/* Icon-only Buttons */
.btn-icon {
  padding: 0.5rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;

  &.btn-sm {
    padding: 0.25rem;
  }

  &.btn-lg {
    padding: 0.75rem;
  }
}
