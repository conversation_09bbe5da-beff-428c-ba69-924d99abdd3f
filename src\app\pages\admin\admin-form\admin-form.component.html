<div class="container mt-4">
  <div class="mb-4 card p-3 shadow-sm">
    <div class="card-header">
      <h4>{{ isEditMode ? ('admin.editAdmin' | translate|async) : ('admin.createAdmin' | translate|async) }}</h4>
    </div>
    <div class="card-body">
      <form [formGroup]="adminForm" (ngSubmit)="onSubmit()">
        <!-- Personal Information Section -->
        <div class="section-container mb-4">
          <h5 class="section-title">{{ 'common.personalInfo' | translate|async }}</h5>
          <div class="row g-3">
            <div class="col-md-6 mb-3">
              <label for="firstName" class="form-label">{{ 'profile.firstName' | translate|async }}</label>
              <input
                type="text"
                class="form-control rounded-pill border-1 border-dark"
                id="firstName"
                formControlName="firstName"
                [ngClass]="{'is-invalid': adminForm.get('firstName')?.invalid && adminForm.get('firstName')?.touched}"
              >
              <div class="invalid-feedback" *ngIf="adminForm.get('firstName')?.errors?.['required']">
                {{ 'profile.enterFirstName' | translate|async }}
              </div>
            </div>

            <div class="col-md-6 mb-3">
              <label for="lastName" class="form-label">{{ 'admin.lastName' | translate|async }}</label>
              <input
                type="text"
                class="form-control rounded-pill"
                id="lastName"
                formControlName="lastName"
                [ngClass]="{'is-invalid': adminForm.get('lastName')?.invalid && adminForm.get('lastName')?.touched}"
              >
              <div class="invalid-feedback" *ngIf="adminForm.get('lastName')?.errors?.['required']">
                {{ 'profile.enterLastName' | translate|async }}
              </div>
            </div>

            <div class="col-md-6 mb-3">
              <label for="phoneNumber" class="form-label">{{ 'common.phone' | translate|async }}</label>
              <input
                type="tel"
                class="form-control rounded-pill"
                id="phoneNumber"
                formControlName="phoneNumber"
                [ngClass]="{'is-invalid': adminForm.get('phoneNumber')?.invalid && adminForm.get('phoneNumber')?.touched}"
              >
              <div class="invalid-feedback" *ngIf="adminForm.get('phoneNumber')?.errors?.['required']">
                {{ 'admin.phoneNumberRequired' | translate|async }}
              </div>
              <div class="invalid-feedback" *ngIf="adminForm.get('phoneNumber')?.errors?.['pattern']">
                {{ 'auth.validation.phoneRequired' | translate|async }}
              </div>
            </div>

            <div class="col-md-6 mb-3">
              <label for="email" class="form-label">{{ 'common.email' | translate|async }}</label>
              <input
                type="email"
                class="form-control rounded-pill"
                id="email"
                formControlName="email"
                [ngClass]="{'is-invalid': adminForm.get('email')?.invalid && adminForm.get('email')?.touched}"
              >
              <div class="invalid-feedback" *ngIf="adminForm.get('email')?.errors?.['required']">
                {{ 'admin.emailRequired' | translate|async }}
              </div>
              <div class="invalid-feedback" *ngIf="adminForm.get('email')?.errors?.['email']">
                {{ 'auth.validation.emailInvalid' | translate|async }}
              </div>
            </div>
          </div>
        </div>

        <!-- Account Credentials Section (Only for New Admins) -->
        <div class="section-container mb-4" *ngIf="!isEditMode">
          <h5 class="section-title">{{ 'common.accountCredentials' | translate|async }}</h5>
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="password" class="form-label">{{ 'admin.password' | translate|async }}</label>
              <div class="input-group">
                <input
                  [type]="showPassword ? 'text' : 'password'"
                  class="form-control rounded-pill rounded-end"
                  id="password"
                  formControlName="password"
                  [ngClass]="{'is-invalid': adminForm.get('password')?.invalid && adminForm.get('password')?.touched}"
                >
                <button class="btn btn-outline-secondary rounded-pill rounded-start" type="button" (click)="togglePasswordVisibility()">
                  <app-bootstrap-icon [name]="showPassword ? 'eye-slash' : 'eye'"></app-bootstrap-icon>
                </button>
                <div class="invalid-feedback" *ngIf="adminForm.get('password')?.errors?.['required']">
                  {{ 'admin.passwordRequired' | translate|async }}
                </div>
                <div class="invalid-feedback" *ngIf="adminForm.get('password')?.errors?.['minlength']">
                  {{ 'admin.passwordMinLength' | translate|async }}
                </div>
                <div class="invalid-feedback" *ngIf="adminForm.get('password')?.errors?.['noUpperCase']">
                  {{ 'admin.passwordUpperCase' | translate|async }}
                </div>
                <div class="invalid-feedback" *ngIf="adminForm.get('password')?.errors?.['noLowerCase']">
                  {{ 'admin.passwordLowerCase' | translate|async }}
                </div>
                <div class="invalid-feedback" *ngIf="adminForm.get('password')?.errors?.['noSpecialChar']">
                  {{ 'admin.passwordSpecialChar' | translate|async }}
                </div>
                <small class="form-text text-muted">
                  {{ 'admin.passwordRequirements' | translate|async }}
                </small>
              </div>
            </div>

            <div class="col-md-6 mb-3">
              <label for="confirmPassword" class="form-label">{{ 'admin.confirmPassword' | translate|async }}</label>
              <div class="input-group">
                <input
                  [type]="showConfirmPassword ? 'text' : 'password'"
                  class="form-control rounded-pill rounded-end"
                  id="confirmPassword"
                  formControlName="confirmPassword"
                  [ngClass]="{'is-invalid': adminForm.get('confirmPassword')?.invalid && adminForm.get('confirmPassword')?.touched}"
                >
                <button class="btn btn-outline-secondary rounded-pill rounded-start" type="button" (click)="toggleConfirmPasswordVisibility()">
                  <app-bootstrap-icon [name]="showConfirmPassword ? 'eye-slash' : 'eye'"></app-bootstrap-icon>
                </button>
                <div class="invalid-feedback" *ngIf="adminForm.get('confirmPassword')?.errors?.['required']">
                  {{ 'admin.confirmPasswordRequired' | translate|async }}
                </div>
                <div class="invalid-feedback" *ngIf="adminForm.get('confirmPassword')?.errors?.['passwordMismatch']">
                  {{ 'admin.passwordMismatch' | translate|async }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Profile Image Section -->
        <div class="section-container mb-4">
          <h5 class="section-title">{{ 'common.profileImage' | translate|async }}</h5>
          <div class="row">
            <div class="col-12">
              <label for="image" class="form-label">{{ 'admin.image' | translate|async }}</label>
              <input
                type="file"
                class="form-control rounded-pill"
                id="image"
                accept="image/*"
                (change)="onImageSelected($event)"
              >
              <div class="mt-3" *ngIf="imagePreview">
                <img [src]="imagePreview" alt="Profile preview" class="profile-preview">
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-end gap-2">
          <app-action-button
            color="secondary"
            icon="cilX"
            [text]="'common.cancel'"
            (clicked)="onCancel()"
            [shape]="'rounded-pill'"
          ></app-action-button>

          <app-action-button
            color="primary"
            type="submit"
            icon="cilSave"
            [text]="isEditMode ? ('common.update') : ('common.create')"
            [disabled]="adminForm.invalid"
            [shape]="'rounded-pill'"
          ></app-action-button>
        </div>
      </form>
    </div>
  </div>
</div>
