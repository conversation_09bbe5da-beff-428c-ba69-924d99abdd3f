<c-row>
  <c-col lg="6">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Badges</strong>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Bootstrap badge scale to suit the size of the parent element by using relative font
          sizing and <code>em</code> units.
        </p>
        <app-docs-example href="components/badge">
          <h1>
            Example heading
            <c-badge color="secondary">New</c-badge>
          </h1>
          <h2>
            Example heading
            <c-badge color="secondary">New</c-badge>
          </h2>
          <h3>
            Example heading
            <c-badge color="secondary">New</c-badge>
          </h3>
          <h4>
            Example heading
            <c-badge color="secondary">New</c-badge>
          </h4>
          <h5>
            Example heading
            <c-badge color="secondary">New</c-badge>
          </h5>
          <h6>
            Example heading
            <c-badge color="secondary">New</c-badge>
          </h6>
        </app-docs-example>
        <p class="text-body-secondary small">
          Badges can be used as part of links or buttons to provide a counter.
        </p>
        <app-docs-example href="components/badge">
          <button cButton color="primary">
            Notifications
            <c-badge color="secondary">4</c-badge>
          </button>
        </app-docs-example>
        <p class="text-body-secondary small">
          Remark that depending on how you use them, badges may be complicated for users of
          screen readers and related assistive technologies.
        </p>
        <p class="text-body-secondary small">
          Unless the context is clear, consider including additional context with a visually
          hidden piece of additional text.
        </p>
        <app-docs-example href="components/badge">
          <button cButton color="primary">
            Profile
            <c-badge color="secondary">9</c-badge>
            <span class="visually-hidden">unread messages</span>
          </button>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col lg="6">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>AngularBadges</strong> <small>Contextual variations</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Add any of the below-mentioned <code>color</code> props to modify the presentation of
          a badge.
        </p>
        <app-docs-example href="components/badge#contextual-variations">
          <c-badge class="me-1" color="primary">primary</c-badge>
          <c-badge class="me-1" color="success">success</c-badge>
          <c-badge class="me-1" color="danger">danger</c-badge>
          <c-badge class="me-1" color="warning">warning</c-badge>
          <c-badge class="me-1" color="info">info</c-badge>
          <c-badge class="me-1" color="light" textColor="dark">light</c-badge>
          <c-badge class="me-1" color="dark">dark</c-badge>
        </app-docs-example>
      </c-card-body>
    </c-card>
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Badges</strong> <small>Pill badges</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Apply the <code>shape=&#34;rounded-pill&#34;</code> prop to make badges rounded.
        </p>
        <app-docs-example href="components/badge#pill-badges">
          <c-badge class="me-1" color="primary" shape="rounded-pill">
            primary
          </c-badge>
          <c-badge class="me-1" color="success" shape="rounded-pill">
            success
          </c-badge>
          <c-badge class="me-1" color="danger" shape="rounded-pill">
            danger
          </c-badge>
          <c-badge class="me-1" color="warning" shape="rounded-pill">
            warning
          </c-badge>
          <c-badge class="me-1" color="info" shape="rounded-pill">
            info
          </c-badge>
          <c-badge class="me-1" color="light" shape="rounded-pill" textColor="dark">
            light
          </c-badge>
          <c-badge class="me-1" color="dark" shape="rounded-pill">
            dark
          </c-badge>
        </app-docs-example>
      </c-card-body>
    </c-card>
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Badges</strong> <small>Positioned</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Use position prop to modify a component and position it in the corner of a link or button.
        </p>
        <app-docs-example href="components/badge#positioned">
          <button cButton color="primary" class="position-relative mx-2">
            Profile
            <c-badge color="danger" position="top-start" shape="rounded-pill">99+</c-badge>
            <span class="visually-hidden">unread messages</span>
          </button>
          <button cButton color="primary" class="position-relative mx-2">
            Profile
            <c-badge color="danger" position="top-end" shape="rounded-pill">99+</c-badge>
            <span class="visually-hidden">unread messages</span>
          </button>
          <br>
          <button cButton color="primary" class="position-relative mx-2">
            Profile
            <c-badge color="danger" position="bottom-start" shape="rounded-pill">99+</c-badge>
            <span class="visually-hidden">unread messages</span>
          </button>
          <button cButton color="primary" class="position-relative mx-2">
            Profile
            <c-badge color="danger" position="bottom-end" shape="rounded-pill">99+</c-badge>
            <span class="visually-hidden">unread messages</span>
          </button>
        </app-docs-example>
      </c-card-body>
    </c-card>
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Angular Badges</strong> <small>Indicator</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          You can also create more generic indicators without a counter using a few more utilities.
        </p>
        <app-docs-example href="components/badge#positioned">
          <button cButton color="primary" class="position-relative">
            Profile
            <c-badge color="danger" position="top-end" shape="rounded-circle" class="p-2" cBorder="light">
              <span class="visually-hidden">unread messages</span>
            </c-badge>
          </button>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>

