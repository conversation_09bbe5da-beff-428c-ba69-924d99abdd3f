/* Table Styles */

// Main table styles
.custom-table {
  width: 100%;
  margin-bottom: 1.5rem;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);

  thead th {
    background: var(--light-gradient);
    color: #495057;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
    padding: 1rem 1.25rem;
    border-bottom: 2px solid #e9ecef;
    white-space: nowrap;
  }

  tbody {
    tr {
      background-color: white;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(0, 0, 0, 0.02);
      }

      &:last-child td {
        border-bottom: none;
      }
    }

    td {
      padding: 1rem 1.25rem;
      vertical-align: middle;
      border-bottom: 1px solid #f8f9fa;
    }
  }
}

// Table container styles
.table-container {
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: var(--card-shadow);

  @media (max-width: 768px) {
    .custom-table {
      min-width: 800px;
    }

    .table-responsive {
      margin-bottom: 0;
    }
  }
}

// Empty state row
.empty-row {
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #6c757d;

    svg, i {
      color: #adb5bd;
      margin-bottom: 1rem;
    }
  }
}
