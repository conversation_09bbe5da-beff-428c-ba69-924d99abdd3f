<!-- Enhanced Sidebar -->
<c-sidebar
  #sidebar1="cSidebar"
  class="d-print-none sidebar sidebar-fixed border-end bg-black"
  colorScheme="dark"
  id="sidebar1"
  visible
>
  <c-sidebar-header class="sidebar-header-custom d-flex align-items-center justify-content-center ">
    <c-sidebar-brand [routerLink]="['/']" class="d-flex align-items-center"  style="background-color: #CD2C4E !important;">
      <img src="assets/images/icons/favicon.svg" alt="ProCar Logo" class="sidebar-brand-icon ">
      <span class="ms-2 fw-semibold d-none d-md-inline fs-lg sidebar-brand-text text-black">ProCar</span>
    </c-sidebar-brand>
  </c-sidebar-header>

  <ng-scrollbar pointerEventsMethod="scrollbar" visibility="hover">
    <ul class="sidebar-nav bg-black">
      <ng-container *ngFor="let item of navItems">
        <!-- Section Title -->
        <li *ngIf="item.title" class="sidebar-title text-uppercase">
          {{ item.name! | translate | async }}
        </li>

        <!-- Nav Item (no children) -->
        <li *ngIf="!item.title && !item.children" class="sidebar-item">
          <a
            [routerLink]="item.url"
            routerLinkActive="active"
            [routerLinkActiveOptions]="{exact: true}"
            class="sidebar-link d-flex align-items-center"
          >
            <div [ngClass]="getIconClass(item)" class="sidebar-icon-wrapper">
              <svg *ngIf="item.iconComponent" cIcon [name]="item.iconComponent.name" width="16" height="16"></svg>
            </div>
            <span class="sidebar-text">{{ item.name! | translate | async }}</span>
            <span *ngIf="item.badge" class="badge ms-auto">{{ item.badge.text }}</span>
          </a>
        </li>

        <!-- Nav Item with Children -->
        <li *ngIf="!item.title && item.children" class="sidebar-item has-submenu">
          <div class="sidebar-link d-flex align-items-center" (click)="toggleSubmenu($event)">
            <div [ngClass]="getIconClass(item)" class="sidebar-icon-wrapper">
              <svg *ngIf="item.iconComponent" cIcon [name]="item.iconComponent.name" width="16" height="16"></svg>
            </div>
            <span class="sidebar-text">{{ item.name! | translate | async }}</span>
            <svg cIcon class="submenu-arrow ms-auto" name="cilChevronRight" width="16" height="16"></svg>
          </div>
          <ul class="sidebar-subnav">
            <li *ngFor="let child of item.children" class="sidebar-subitem">
              <a
                [routerLink]="child.url"
                routerLinkActive="active"
                class="sidebar-link d-flex align-items-center"
              >
                <span>{{ child.name! | translate | async }}</span>
                <span *ngIf="child.badge" class="badge ms-auto">{{ child.badge.text }}</span>
              </a>
            </li>
          </ul>
        </li>
      </ng-container>
    </ul>
  </ng-scrollbar>


</c-sidebar>

<!-- Main Layout -->
<div class="wrapper d-flex flex-column min-vh-100">
  <!--app-header-->
  <app-default-header
    [cShadowOnScroll]="'sm'"
    class="mb-4 d-print-none header header-sticky p-0 shadow-sm"
    position="sticky"
    sidebarId="sidebar1"
  />
  <!--app-body-->
  <div class="body flex-grow-1">
    <c-container breakpoint="lg" class="h-auto px-4">
      <router-outlet />
    </c-container>
  </div>
  <!--app footer-->
  <app-default-footer />
</div>
