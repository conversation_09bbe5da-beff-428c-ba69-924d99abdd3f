:host {
  display: inline-block;
}

button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  font-weight: 500;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);

  // Standard button sizing for consistency
  min-height: 38px;
  padding: 0.5rem 1.25rem;
  font-size: 0.9rem;
  line-height: 1.5;

  // Hover effects for all buttons
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  &:active:not(:disabled) {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  }

  &:focus {
    box-shadow: 0 0 0 3px rgba(var(--cui-primary-rgb), 0.25);
  }

  // Icon-only button styling
  &.icon-only {
    width: 2.5rem;
    height: 2.5rem;
    padding: 0;
    min-height: 2.5rem;

    &.btn-sm {
      width: 2rem;
      height: 2rem;
      min-height: 2rem;
    }

    &.btn-lg {
      width: 3rem;
      height: 3rem;
      min-height: 3rem;
    }

    svg {
      margin: 0 !important;
    }
  }

  // Small button adjustments
  &.btn-sm:not(.icon-only) {
    min-height: 32px;
    padding: 0.375rem 1rem;
    font-size: 0.85rem;
  }

  // Large button adjustments
  &.btn-lg:not(.icon-only) {
    min-height: 44px;
    padding: 0.625rem 1.5rem;
    font-size: 1rem;
  }

  // Block button
  &.btn-block {
    width: 100%;
  }

  // Icon spacing
  svg {
    flex-shrink: 0;

    &:not(.icon-only svg) {
      margin-right: 0.5rem;
    }
  }

  // Loading spinner
  .spinner-border {
    width: 1rem;
    height: 1rem;
    border-width: 0.15em;
  }

  // Disabled state
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
  }
}

/* RTL Support */
:host-context(html[lang="ar"]) {
  button {
    svg:not(.icon-only svg) {
      margin-right: 0;
      margin-left: 0.5rem;
    }
  }
}

/* Responsive adjustments */
@media (max-width: 767px) {
  button {
    min-height: 36px;
    padding: 0.375rem 1rem;
    font-size: 0.85rem;

    &.btn-sm:not(.icon-only) {
      min-height: 30px;
      padding: 0.25rem 0.75rem;
      font-size: 0.8rem;
    }

    &.btn-lg:not(.icon-only) {
      min-height: 40px;
      padding: 0.5rem 1.25rem;
      font-size: 0.9rem;
    }
  }
}

/* Focus Styles */
button:focus-visible {
  box-shadow: 0 0 0 0.25rem rgba(var(--cui-primary-rgb), 0.5);
  outline: none;
}
