import { Injectable } from '@angular/core';
import { CanActivate, Router, UrlTree } from '@angular/router';
import { Store } from '@ngrx/store';
import { Observable, map, take } from 'rxjs';
import { selectAuthResponse } from '../store/auth/auth.selectors';
import { Roles } from '../Enums/Roles.enum';
import { clearAuthStore } from '../store/auth/auth.actions';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  constructor(
    private store: Store,
    private router: Router
  ) {}

  canActivate(): Observable<boolean | UrlTree> {
    return this.store.select(selectAuthResponse).pipe(
      take(1),
      map(response => {
        console.log("Auth guard: Checking auth response", response);
        if (response?.token && response?.role === Roles.Admin) {
          console.log("Auth guard: User authenticated, allowing access");
          return true;
        } else {
          console.log("Auth guard: User not authenticated, redirecting to login");

          // Only clear auth store if we actually have stale data
          const hasLocalToken = localStorage.getItem('token') || localStorage.getItem('authToken');
          const hasLocalUserData = localStorage.getItem('user');

          if (hasLocalToken || hasLocalUserData) {
            console.log('Auth guard: Found stale auth data, clearing it');
            this.store.dispatch(clearAuthStore());
          }

          // Always redirect to login for unauthenticated users
          return this.router.createUrlTree(['/login']);
        }
      })
    );
  }


}
