import { Injectable } from '@angular/core';
import { CanActivate, Router, UrlTree } from '@angular/router';
import { Store } from '@ngrx/store';
import { Observable, map, take } from 'rxjs';
import { selectAuthResponse } from '../store/auth/auth.selectors';
import { Roles } from '../Enums/Roles.enum';
import { checkAuthFailure } from '../store/auth/auth.actions';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  constructor(
    private store: Store,
    private router: Router
  ) {}

  canActivate(): Observable<boolean | UrlTree> {
    return this.store.select(selectAuthResponse).pipe(
      take(1),
      map(response => {
        console.log("response from auth guard", response);
        if (response?.token && response?.role === Roles.Admin) {
          return true;
        } else {
          // Only clear auth data and dispatch failure if we actually have stale data
          // This prevents unnecessary clearing when the interceptor has already handled it
          const hasLocalToken = localStorage.getItem('token') || localStorage.getItem('authToken');
          const hasLocalUserData = localStorage.getItem('user');

          if (hasLocalToken || hasLocalUserData) {
            console.log('Auth guard: Clearing stale auth data');
            this.clearAuthData();
            this.store.dispatch(checkAuthFailure());
          }

          return this.router.createUrlTree(['/login']);
        }
      })
    );
  }

  /**
   * Clear only authentication-related data from localStorage
   * Preserves non-auth data like theme and language preferences
   */
  private clearAuthData(): void {
    const authKeys = [
      'token',
      'authToken',
      'user',
      'fcmToken',
      'fcmTokenData'
    ];

    authKeys.forEach(key => {
      localStorage.removeItem(key);
    });

    console.log('Auth data cleared by auth guard');
  }
}
