.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border: none;
  border-radius: 0.5rem;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  }

  .card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
  }
}

.table {
  margin-bottom: 0;

  th {
    font-weight: 600;
    border-bottom-width: 1px;
    padding: 1rem;
    white-space: nowrap;
  }

  td {
    padding: 1rem;
    vertical-align: middle;
  }
}

// Enhanced button styling
button[cButton] {
  position: relative;
  overflow: hidden;
  transition: all 0.25s ease-in-out;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
  }

  &[size="sm"] {
    font-size: 0.85rem;
  }

  &[shape="rounded-pill"] {
    border-radius: 50rem;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  // Add ripple effect
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
  }

  &:focus:not(:active)::after {
    animation: ripple 0.8s ease-out;
  }
}

// Ripple animation
@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  20% {
    transform: scale(25, 25);
    opacity: 0.5;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}

// Action cell in table
.actions-cell {
  text-align: right;
  white-space: nowrap;
  min-width: 180px;

  button {
    display: inline-flex;
    align-items: center;

    svg {
      transition: transform 0.2s ease;
    }

    &:hover svg {
      transform: scale(1.2);
    }
  }
}

// Avatar styling
.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.1);
    border-color: var(--cui-primary);
  }

  // Add subtle indicator for default avatar
  &.default-avatar {
    border: 2px dashed var(--cui-primary-light, #70a6e8);
    box-shadow: 0 0 0 2px rgba(var(--cui-primary-rgb), 0.1);
  }
}

.btn-group {
  gap: 0.5rem;
}

.pagination {
  margin-bottom: 0;

  .page-link {
    padding: 0.5rem 0.75rem;
    color: var(--cui-primary);
    border: 1px solid var(--cui-border-color);
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--cui-light);
      border-color: var(--cui-border-color);
    }
  }

  .page-item.active .page-link {
    background-color: var(--cui-primary);
    border-color: var(--cui-primary);
  }

  .page-item.disabled .page-link {
    color: var(--cui-gray-500);
    pointer-events: none;
  }
}

.input-group {
  .input-group-text {
    background-color: transparent;
    border-right: none;
  }

  .form-control {
    border-left: none;
    padding-left: 0;

    &:focus {
      box-shadow: none;
      border-color: var(--cui-border-color);
    }
  }
}

// Loading spinner animation
.spinner-border {
  width: 3rem;
  height: 3rem;
  border-width: 0.25rem;
}

// Empty state styling
.text-center {
  i.cil-people {
    opacity: 0.5;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .card-header {
    padding: 1rem;
  }

  .table {
    th, td {
      padding: 0.75rem;
    }
  }

  .actions-cell {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
  }

  .avatar {
    width: 2rem;
    height: 2rem;
  }
}

// Animation for table rows
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.table tbody tr {
  animation: fadeIn 0.3s ease-out;
  animation-fill-mode: both;

  @for $i from 1 through 10 {
    &:nth-child(#{$i}) {
      animation-delay: #{$i * 0.05}s;
    }
  }
}
