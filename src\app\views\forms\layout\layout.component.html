<c-row>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Layout</strong> <small>Form grid</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          More complex forms can be built using our grid classes. Use these for form layouts
          that require multiple columns, varied widths, and additional alignment options.
        </p>
        <app-docs-example href="forms/layout#form-grid">
          <c-row>
            <c-col xs>
              <input aria-label="First name" cFormControl placeholder="First name" />
            </c-col>
            <c-col xs>
              <input aria-label="Last name" cFormControl placeholder="Last name" />
            </c-col>
          </c-row>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Layout</strong> <small>Gutters</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          By adding <a href="https://coreui.io/docs/layout/gutters/">gutter modifier classes</a>
          , you can have control over the gutter width in as well the inline as block direction.
        </p>
        <app-docs-example href="forms/layout#gutters">
          <c-row class="g-3">
            <c-col xs>
              <input aria-label="First name" cFormControl placeholder="First name" />
            </c-col>
            <c-col xs>
              <input aria-label="Last name" cFormControl placeholder="Last name" />
            </c-col>
          </c-row>
        </app-docs-example>
        <p class="text-body-secondary small">
          More complex layouts can also be created with the grid system.
        </p>
        <app-docs-example href="forms/layout#gutters">
          <form cForm class="row g-3">
            <c-col md="6">
              <label cLabel for="inputEmail4">Email</label>
              <input cFormControl id="inputEmail4" type="email" />
            </c-col>
            <c-col md="6">
              <label cLabel for="inputPassword4">Password</label>
              <input cFormControl id="inputPassword4" type="password" />
            </c-col>
            <c-col xs="12">
              <label cLabel for="inputAddress">Address</label>
              <input cFormControl id="inputAddress" placeholder="1234 Main St" />
            </c-col>
            <c-col xs="12">
              <label cLabel for="inputAddress2">Address 2</label>
              <input cFormControl id="inputAddress2" placeholder="Apartment, studio, or floor" />
            </c-col>
            <c-col md="6">
              <label cLabel for="inputCity">City</label>
              <input cFormControl id="inputCity" />
            </c-col>
            <c-col md="4">
              <label cLabel for="inputState">State</label>
              <select cSelect id="inputState">
                <option>Choose...</option>
                <option>...</option>
              </select>
            </c-col>
            <c-col md="2">
              <label cLabel for="inputZip">Zip</label>
              <input cFormControl id="inputZip" />
            </c-col>
            <c-col xs="12">
              <c-form-check>
                <input cFormCheckInput id="gridCheck" name="gridCheck" type="checkbox" />
                <label cFormCheckLabel for="gridCheck">Check me out</label>
              </c-form-check>
            </c-col>
            <c-col xs="12">
              <button cButton type="submit">Sign in</button>
            </c-col>
          </form>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Layout</strong> <small>Horizontal form</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Create horizontal forms with the grid by adding the <code>.row</code> class to form
          groups and using the <code>.col-*-*</code> classes to specify the width of your labels
          and controls. Be sure to add <code>.col-form-label</code> to your
          <code>&lt;label&gt;</code>s as well so they&#39;re vertically centered with their
          associated form controls.
        </p>
        <p class="text-body-secondary small">
          At times, you maybe need to use margin or padding utilities to create that perfect
          alignment you need. For example, we&#39;ve removed the <code>padding-top</code> on our
          stacked radio inputs label to better align the text baseline.
        </p>
        <app-docs-example href="forms/layout#horizontal-form">
          <form>
            <c-row class="mb-3">
              <label cLabel class="col-sm-2 col-form-label" for="inputEmail3">
                Email
              </label>
              <c-col sm="10">
                <input cFormControl id="inputEmail3" type="email" />
              </c-col>
            </c-row>
            <c-row class="mb-3">
              <label cLabel class="col-sm-2 col-form-label" for="inputPassword3">
                Password
              </label>
              <c-col sm="10">
                <input cFormControl id="inputPassword3" type="password" />
              </c-col>
            </c-row>
            <fieldset class="row mb-3">
              <legend class="col-form-label col-sm-2 pt-0">Radios</legend>
              <c-col sm="10">
                <c-form-check>
                  <input cFormCheckInput checked id="gridRadios1" name="gridRadios" type="radio" value="option1" />
                  <label cFormCheckLabel for="gridRadios1">First radio</label>
                </c-form-check>
                <c-form-check>
                  <input cFormCheckInput id="gridRadios2" name="gridRadios" type="radio" value="option2" />
                  <label cFormCheckLabel for="gridRadios2">Second radio</label>
                </c-form-check>
                <c-form-check>
                  <input cFormCheckInput id="gridRadios3" name="gridRadios" type="radio" value="option3" />
                  <label cFormCheckLabel for="gridRadios3">Third disabled radio</label>
                </c-form-check>
              </c-col>
            </fieldset>
            <c-row class="mb-3">
              <c-col sm="10" class="offset-sm-2">
                <c-form-check>
                  <input cFormCheckInput id="gridCheck1" name="gridCheck1" type="checkbox" />
                  <label cFormCheckLabel for="gridCheck1">Example checkbox</label>
                </c-form-check>
              </c-col>
            </c-row>
            <button cButton type="submit">Sign in</button>
          </form>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Layout</strong> <small>Horizontal form label sizing</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Be sure to use <code>.col-form-label-sm</code> or <code>.col-form-label-lg</code> to
          your <code>&lt;label&gt;</code>s or <code>&lt;legend&gt;</code>s to correctly
          follow the size of <code>.form-control-lg</code> and <code>.form-control-sm</code>.
        </p>
        <app-docs-example href="forms/layout#horizontal-form-label-sizing">
          <c-row class="mb-3">
            <label cLabel="col" cCol sm="2" sizing="sm" for="colFormLabelSm">
              Email
            </label>
            <c-col sm="10">
              <input cFormControl
                     sizing="sm"
                     id="colFormLabelSm"
                     placeholder="col-form-label-sm"
                     type="email"
              />
            </c-col>
          </c-row>
          <c-row class="mb-3">
            <label cLabel="col" cCol sm="2" for="colFormLabel">
              Email
            </label>
            <c-col sm="10">
              <input cFormControl id="colFormLabel" placeholder="col-form-label" type="email" />
            </c-col>
          </c-row>
          <c-row>
            <label cLabel="col" cCol sm="2" sizing="lg" for="colFormLabelLg">
              Email
            </label>
            <c-col sm="10">
              <input cFormControl
                     sizing="lg"
                     id="colFormLabelLg"
                     placeholder="col-form-label-lg"
                     type="email"
              />
            </c-col>
          </c-row>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Layout</strong> <small>Column sizing</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          As shown in the previous examples, our grid system allows you to place any number of
          <code>&lt;c-col&gt;</code>s within a <code>&lt;c-row&gt;</code>. They&#39;ll split the
          available width equally between them. You may also pick a subset of your columns to
          take up more or less space, while the remaining <code>&lt;c-col&gt;</code>s equally
          split the rest, with specific column classes like
          <code>&lt;c-col sm=&#34;7&#34;&gt;</code>.
        </p>
        <app-docs-example href="forms/layout#column-sizing">
          <c-row class="g-3">
            <c-col sm="7">
              <input aria-label="City" cFormControl placeholder="City" />
            </c-col>
            <c-col sm>
              <input aria-label="State" cFormControl placeholder="State" />
            </c-col>
            <c-col sm>
              <input aria-label="Zip" cFormControl placeholder="Zip" />
            </c-col>
          </c-row>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Layout</strong> <small>Auto-sizing</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          The example below uses a flexbox utility to vertically center the contents and changes
          <code>&lt;c-col&gt;</code> to <code>&lt;c-col xs=&#34;auto&#34;&gt;</code> so that your
          columns only take up as much space as needed. Put another way, the column sizes itself
          based on the contents.
        </p>
        <app-docs-example href="forms/layout#auto-sizing">
          <form cForm class="row gy-2 gx-3 align-items-center">
            <c-col xs="auto">
              <label cLabel class="visually-hidden" for="autoSizingInput">
                Name
              </label>
              <input cFormControl id="autoSizingInput" placeholder="Jane Doe" />
            </c-col>
            <c-col xs="auto">
              <label cLabel class="visually-hidden" for="autoSizingInputGroup">
                Username
              </label>
              <c-input-group>
                <span cInputGroupText>&#64;</span>
                <input cFormControl id="autoSizingInputGroup" placeholder="Username" />
              </c-input-group>
            </c-col>
            <c-col xs="auto">
              <label cLabel class="visually-hidden" for="autoSizingSelect">
                Preference
              </label>
              <select cSelect id="autoSizingSelect">
                <option>Choose...</option>
                <option value="1">One</option>
                <option value="2">Two</option>
                <option value="3">Three</option>
              </select>
            </c-col>
            <c-col xs="auto">
              <c-form-check>
                <input cFormCheckInput id="autoSizingCheck" name="autoSizingCheck" type="checkbox" />
                <label cFormCheckLabel for="autoSizingCheck">Remember me</label>
              </c-form-check>
            </c-col>
            <c-col xs="auto">
              <button cButton type="submit">Submit</button>
            </c-col>
          </form>
        </app-docs-example>
        <p class="text-body-secondary small">
          You can then remix that once again with size-specific column classes.
        </p>
        <app-docs-example href="forms/layout#auto-sizing">
          <form cForm class="row gx-3 gy-2 align-items-center">
            <c-col sm="3">
              <label cLabel class="visually-hidden" for="specificSizeInputName">
                Name
              </label>
              <input cFormControl id="specificSizeInputName" placeholder="Jane Doe" />
            </c-col>
            <c-col sm="3">
              <label cLabel class="visually-hidden" for="specificSizeInputGroupUsername">
                Username
              </label>
              <c-input-group>
                <span cInputGroupText>&#64;</span>
                <input cFormControl id="specificSizeInputGroupUsername" placeholder="Username" />
              </c-input-group>
            </c-col>
            <c-col sm="3">
              <label cLabel class="visually-hidden" for="specificSizeSelect">
                Preference
              </label>
              <select cSelect id="specificSizeSelect">
                <option>Choose...</option>
                <option value="1">One</option>
                <option value="2">Two</option>
                <option value="3">Three</option>
              </select>
            </c-col>
            <c-col xs="auto">
              <c-form-check>
                <input cFormCheckInput id="autoSizingCheck2" name="autoSizingCheck2" type="checkbox" />
                <label cFormCheckLabel for="autoSizingCheck2">Remember me</label>
              </c-form-check>
            </c-col>
            <c-col xs="auto">
              <button cButton type="submit">Submit</button>
            </c-col>
          </form>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
  <c-col xs="12">
    <c-card class="mb-4">
      <c-card-header ngPreserveWhitespaces>
        <strong>Layout</strong> <small>Inline forms</small>
      </c-card-header>
      <c-card-body>
        <p class="text-body-secondary small">
          Use the <code>&lt;c-col xs=&#34;auto&#34;&gt;</code> class to create horizontal
          layouts. By adding
          <a href="https://coreui.io/docs/layout/gutters/">gutter modifier classes</a>, we will
          have gutters in horizontal and vertical directions. The
          <code>.align-items-center</code> aligns the form elements to the middle, making the
          <code>&lt;CFormCheck&gt;</code> align properly.
        </p>
        <app-docs-example href="forms/layout#inline-forms">
          <form cForm class="row row-cols-lg-auto g-3 align-items-center">
            <c-col xs="12">
              <label cLabel class="visually-hidden" for="inlineFormInputGroupUsername">
                Username
              </label>
              <c-input-group>
                <span cInputGroupText>&#64;</span>
                <input cFormControl id="inlineFormInputGroupUsername" placeholder="Username" />
              </c-input-group>
            </c-col>
            <c-col xs="12">
              <label cLabel class="visually-hidden" for="inlineFormSelectPref">
                Preference
              </label>
              <select cSelect id="inlineFormSelectPref">
                <option>Choose...</option>
                <option value="1">One</option>
                <option value="2">Two</option>
                <option value="3">Three</option>
              </select>
            </c-col>
            <c-col xs="12">
              <c-form-check inline>
                <input cFormCheckInput id="inlineFormCheck" name="inlineFormCheck" type="checkbox" />
                <label cFormCheckLabel for="inlineFormCheck">Remember me</label>
              </c-form-check>
            </c-col>
            <c-col xs="12">
              <button cButton type="submit">Submit</button>
            </c-col>
          </form>
        </app-docs-example>
      </c-card-body>
    </c-card>
  </c-col>
</c-row>
