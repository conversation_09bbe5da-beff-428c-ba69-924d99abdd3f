{"common": {"noResults": "No Results", "tryDifferentSearch": "Try Different Search", "AddToLandingPage": "Add to Landing Page", "dashboard": "Dashboard", "settings": "Settings", "profile": "Profile", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "search": "Search", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "loading": "Loading...", "noData": "No Data", "error": "An unexpected error occurred. Please try again.", "success": "Success", "warning": "Warning", "info": "Information", "previous": "Previous", "next": "Next", "first": "First", "last": "Last", "show": "Show", "entries": "Entries", "showing": "Showing", "to": "to", "of": "of", "page": "Page", "firstPage": "First Page", "previousPage": "Previous Page", "nextPage": "Next Page", "lastPage": "Last Page", "firstPageTooltip": "First Page", "previousPageTooltip": "Previous Page", "nextPageTooltip": "Next Page", "lastPageTooltip": "Last Page", "allGovernorates": "All Governorates", "actions": "Actions", "preview": "Preview", "remove": "Remove", "close": "Close", "confirm": "Confirm", "reset": "Reset", "uploadIcon": "Upload Icon", "update": "Update", "add": "Add", "back": "Back", "finish": "Finish", "account": "Account", "toggleSidebar": "Toggle sidebar navigation", "openUserMenu": "Open user menu", "userAvatar": "User avatar", "switchLanguage": "Switch Language", "openThemePicker": "Open theme picker", "messages": {"updates": "Updates", "messages": "Messages", "tasks": "Tasks", "comments": "Comments"}, "loadingMore": "Loading more...", "allNotificationsLoaded": "All notifications loaded", "notifications": "Notifications", "markAllRead": "<PERSON>", "noNotifications": "No notifications", "viewAll": "View All", "notificationTypes": {"newRequest": "New Request", "requestAccepted": "Request Accepted", "requestRejected": "Request Rejected", "requestCancelled": "Request Cancelled", "requestCompleted": "Request Completed", "newReservation": "New Reservation"}, "clear": "Clear", "refresh": "Refresh", "view": "View", "yes": "Yes", "no": "No", "unknown": "Unknown", "status": {"cpuUsage": "CPU Usage", "memoryUsage": "Memory Usage", "ssdUsage": "SSD Usage", "processes": "348 Processes. 1/4 Cores.", "memory": "11444GB/16384MB", "storage": "243GB/256GB"}, "tasks": {"upgradeNpm": "Upgrade NPM", "reactVersion": "ReactJS Version", "vueVersion": "VueJS Version", "newLayouts": "Add new layouts", "angularVersion": "Angular Version"}, "time": {"42": "42"}, "colors": "Colors", "typography": "Typography", "components": "Components", "base": "Base", "accordion": "Accordion", "breadcrumbs": "Breadcrumbs", "cards": "Cards", "carousel": "Carousel", "collapse": "Collapse", "listGroup": "List Group", "navsTabs": "Navs & Tabs", "pagination": "Pagination", "placeholder": "Placeholder", "popovers": "Popovers", "progress": "Progress", "spinners": "Spinners", "tables": "Tables", "tabs": "Tabs", "tooltips": "Tooltips", "buttons": "Buttons", "buttonGroups": "Button groups", "dropdowns": "Dropdowns", "forms": "Forms", "formControl": "Form Control", "select": "Select", "checksRadios": "Checks & Radios", "range": "Range", "inputGroup": "Input Group", "floatingLabels": "Floating Labels", "layout": "Layout", "validation": {"required": "This field is required"}, "charts": "Charts", "icons": "Icons", "coreuiFree": "CoreUI Free", "coreuiFlags": "CoreUI Flags", "coreuiBrands": "CoreUI Brands", "alerts": "<PERSON><PERSON><PERSON>", "badges": "Badges", "modal": "Modal", "toast": "Toast", "widgets": "Widgets", "extras": "Extras", "pages": "Pages", "error404": "Error 404", "error500": "Error 500", "links": "Links", "docs": "Docs", "confirmDelete": "Are you sure you want to delete this item?", "name": "Name", "email": "Email", "phone": "Phone", "role": "Role", "active": "Active", "inactive": "Inactive", "showPassword": "Show password", "hidePassword": "Hide password", "all": "All", "createdAt": "Created At", "createdBy": "Created By", "noDataFound": "No data found", "personalInfo": "Personal Information", "accountCredentials": "Account Credentials", "profileImage": "Profile Image"}, "profile": {"security": "Security", "passwordRequirements": "Password Requirements", "enterCurrentPassword": "Enter your current password", "enterNewPassword": "Enter your new password", "enterConfirmPassword": "Enter your confirm password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm Password", "changePassword": "Change Password", "manageYourProfile": "Manage your personal information and account settings", "personalInformation": "Personal Information", "accountInfo": "Account Information", "accountStatus": "Account Status", "memberSince": "Member Since", "lastLogin": "Last Login", "statistics": "Statistics", "totalRequests": "Total Requests", "completedRequests": "Completed Requests", "pendingRequests": "Pending Requests", "totalReservations": "Total Reservations", "changePhoto": "Change Photo", "firstName": "First Name", "lastName": "Last Name", "enterFirstName": "Enter your first name", "enterLastName": "Enter your last name", "enterPhone": "Enter your phone number", "cancelConfirmation": "Are you sure you want to cancel? All unsaved changes will be lost.", "saveConfirmation": "Are you sure you want to save these changes?", "profileUpdated": "Profile updated successfully", "profileUpdateError": "Failed to update profile. Please try again.", "imageUploadError": "Failed to upload image. Please try again.", "invalidImageType": "Please select a valid image file (JPG, PNG, GIF)", "imageTooLarge": "Image size must be less than 5MB", "nameRequired": "Name is required", "emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "phoneInvalid": "Please enter a valid phone number"}, "notifications": {"title": "Notifications", "manageAllNotifications": "Manage your notifications", "unread": "unread", "searchPlaceholder": "Search notifications...", "filterByType": "Filter by Type", "filterByStatus": "Filter by Status", "noNotificationsFound": "No Notifications Found", "tryDifferentFilters": "Try adjusting your filters or search terms", "noNotificationsYet": "You don't have any notifications yet", "clearFilters": "Clear Filters", "showingResults": "Showing", "pageSize": "Items per page", "new": "New", "markRead": "<PERSON> <PERSON>", "read": "Read", "allTypes": "All Types", "allStatus": "All Status", "timeFormat": {"justNow": "Just now", "minutesAgo": "{0}m ago", "hoursAgo": "{0}h ago", "daysAgo": "{0}d ago"}}, "theme": {"light": "Light", "dark": "Dark", "auto": "Auto"}, "auth": {"email": "Email", "login": "Sign In", "signIn": "Sign In", "signUp": "Sign Up", "phoneNumber": "Phone Number", "EmailPlaceholder": "Enter your Email", "password": "Password", "passwordPlaceholder": "Enter your password", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "loginError": "Login Failed", "secureLogin": "<PERSON><PERSON>", "secureConnection": "Secure Connection", "dataProtection": "Data Protection", "invalidCredentials": "Phone number or password is wrong", "loginFailed": "<PERSON><PERSON> failed. Please try again.", "signInMessage": "Sign In to your account", "signUpMessage": "Create a new account to get started with our platform. Join us today and experience the benefits of our services.", "registerNow": "Register Now!", "forgotPasswordMessage": "Enter your email address and we'll send you a verification code to reset your password.", "emailPlaceholder": "Enter your email address", "sendOtp": "Send Verification Code", "verifyOtp": "Verify Code", "otpCode": "Verification Code", "otpSentTo": "We've sent a 4-digit verification code to", "resendOtp": "Resend Code", "resendIn": "Resend in", "resetPassword": "Reset Password", "resetPasswordMessage": "Enter your new password to complete the reset process.", "newPassword": "New Password", "newPasswordPlaceholder": "Enter your new password", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Confirm your new password", "validation": {"emailInvalid": "Please enter a valid email address", "phoneRequired": "Please enter a valid phone number"}}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "Minimum length is {0} characters", "maxLength": "Maximum length is {0} characters", "egyptPhone": "Please enter a valid Egyptian phone number (e.g., 01012345678)", "phoneError": "Phone Number Error", "passwordMinLength": "Password must be at least 6 characters long", "phoneRequired": "Phone number is required", "passwordRequired": "Password is required", "currentPasswordRequired": "Current password is required", "newPasswordRequired": "New password is required", "confirmPasswordRequired": "Password confirmation is required", "passwordMismatch": "Passwords do not match"}, "admin": {"email": "Email", "title": "Admins Management", "searchPlaceholder": "Search by name or phone number", "search": "Search", "create": "Create Admin", "name": "Name", "phone": "Phone Number", "image": "Image", "actions": "Actions", "edit": "Edit", "delete": "Delete", "confirmDelete": "Are you sure you want to delete this admin?", "firstName": "First Name", "lastName": "Last Name", "password": "Password", "confirmPassword": "Confirm Password", "imageUpload": "Upload Image", "noImage": "No Image", "noResults": "No admins found", "tryDifferentSearch": "Try different search", "editTooltip": "Edit Admin", "deleteTooltip": "Delete Admin", "passwordUpperCase": "Password must contain at least one uppercase letter", "passwordLowerCase": "Password must contain at least one lowercase letter", "passwordSpecialChar": "Password must contain at least one special character", "passwordRequirements": "Password must be at least 8 characters long and contain uppercase, lowercase, and special characters", "createAdmin": "Create Admin", "editAdmin": "Edit Admin", "phoneNumberRequired": "Phone number is required", "emailRequired": "Email is required", "passwordRequired": "Password is required", "passwordMinLength": "Password must be at least 8 characters long", "passwordMismatch": "Passwords do not match", "confirmPasswordRequired": "Please confirm your password"}, "location": {"title": "Location Management", "governorates": "Governorates", "cities": "Cities", "addGovernorate": "Add Governorate", "addCity": "Add City", "editGovernorate": "Edit Governorate", "editCity": "Edit City", "updateGovernorate": "Update Governorate", "updateCity": "Update City", "governorateName": "Governorate Name", "cityName": "City Name", "selectGovernorate": "Select Governorate", "noGovernorates": "No governorates found", "noCities": "No cities found for this governorate", "loading": "Loading...", "error": "Error loading data", "deleteGovernorateConfirm": "Are you sure you want to delete this governorate?", "deleteCityConfirm": "Are you sure you want to delete this city?", "deleteGovernorateSuccess": "Governorate deleted successfully", "deleteCitySuccess": "City deleted successfully", "deleteGovernorateError": "Error deleting governorate", "deleteCityError": "Error deleting city", "selectGovernorateFirst": "Please select a governorate first", "addCityToStart": "Add New City", "selectGovernorateInstruction": "Select a governorate to add a city", "allGovernorates": "All Governorates"}, "disease": {"title": "Disease Management", "searchPlaceholder": "Search by disease name", "create": "Add Disease", "edit": "Edit Disease", "nameEn": "English Name", "nameAr": "Arabic Name", "nameEnPlaceholder": "Enter disease name in English", "nameArPlaceholder": "Enter disease name in Arabic", "confirmDelete": "Are you sure you want to delete this disease?", "noResults": "No diseases found", "tryDifferentSearch": "Try a different search term", "deleteSuccess": "Disease deleted successfully", "addSuccess": "Disease added successfully", "updateSuccess": "Disease updated successfully", "update": "Update"}, "serviceCategory": {"title": "Service Categories Management", "noSubCategoriesDescription": "No sub-categories available", "description": "Manage and organize your service categories effectively", "add": "Add New Category", "edit": "Edit Category", "delete": "Delete Category", "nameAr": "Category Name (Arabic)", "nameEn": "Category Name (English)", "descriptionAr": "Category Description (Arabic)", "descriptionEn": "Category Description (English)", "icon": "Category Icon", "image": "Category Image", "actions": "Available Actions", "confirmDelete": "Are you sure you want to delete this category?", "noData": "No service categories available", "empty": "No Service Categories", "emptyDescription": "Start by adding new service categories to organize your services", "addFirst": "Add First Category", "addSubCategory": "Add Sub-Category", "nameArPlaceholder": "Enter category name in Arabic", "nameEnPlaceholder": "Enter category name in English", "descriptionArPlaceholder": "Enter category description in Arabic", "descriptionEnPlaceholder": "Enter category description in English", "subCategories": "Sub-Categories", "deleteTitle": "Delete Category", "deleteConfirmation": "This category and all its associated sub-categories will be deleted. Are you sure you want to proceed?", "loading": "Loading categories...", "error": "Error loading categories", "success": "Category saved successfully", "errorSaving": "Error saving category", "errorDeleting": "Error deleting category", "successDeleting": "Category deleted successfully", "searchPlaceholder": "Search categories...", "noResults": "No results found", "parentCategory": "Parent Category", "selectParentCategory": "Select Parent Category", "noParentCategory": "No parent category", "iconUpload": "Upload Icon", "iconPreview": "Preview Icon", "iconRemove": "Remove Icon", "iconRequired": "Icon is required", "nameRequired": "Category name is required", "descriptionRequired": "Category description is required", "maxLength": "Maximum length is {0} characters", "minLength": "Minimum length is {0} characters", "invalidIcon": "Invalid icon format", "iconSize": "Icon size must not exceed {0} KB", "iconTypes": "Allowed icon types: {0}", "save": "Save Category", "editSuccess": "Category updated successfully", "addSuccess": "Category added successfully", "deleteSuccess": "Category deleted successfully", "editError": "Error updating category", "addError": "Error adding category", "deleteError": "Error deleting category", "confirmDeleteSubCategory": "Are you sure you want to delete this sub-category?", "deleteSubCategorySuccess": "Sub-category deleted successfully", "deleteSubCategoryError": "Error deleting sub-category", "addSubCategorySuccess": "Sub-category added successfully", "addSubCategoryError": "Error adding sub-category", "editSubCategorySuccess": "Sub-category updated successfully", "editSubCategoryError": "Error updating sub-category", "viewSubCategories": "View", "noSubCategories": "No sub-categories available", "fromCallCenter": "From Call Center"}, "users": {"title": "Users"}, "specialty": {"title": "Specialties Management", "add": "Add Specialty", "edit": "Edit", "delete": "Delete", "nameAr": "Specialty Name (Arabic)", "nameEn": "Specialty Name (English)", "descriptionAr": "Specialty Description (Arabic)", "descriptionEn": "Specialty Description (English)", "actions": "Actions", "confirmDelete": "Are you sure you want to delete this specialty?", "noData": "No specialties available", "addSuccess": "Specialty added successfully", "editSuccess": "Specialty updated successfully", "deleteSuccess": "Specialty deleted successfully", "addError": "Error adding specialty", "editError": "Error updating specialty", "deleteError": "Error deleting specialty", "nameArRequired": "Specialty name in Arabic is required", "nameEnRequired": "Specialty name in English is required"}, "nurse": {"title": "Nurses Management", "add": "Add Nurse", "edit": "Edit Nurse", "delete": "Delete Nurse", "confirmDelete": "Are you sure you want to delete this nurse?", "personalInfo": "Personal Information", "professionalInfo": "Professional Information", "locationInfo": "Location Information", "accountCredentials": "Account Credentials", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone Number", "password": "Password", "confirmPassword": "Confirm Password", "specialization": "Specialization", "selectSpecialization": "Select Specialization", "governorate": "Governorate", "selectGovernorate": "Select Governorate", "city": "City", "selectCity": "Select City", "license": "Medical License", "mapLocation": "Map Location", "profilePicture": "Profile Picture", "imageRequirements": "Upload a clear profile picture (JPG, PNG, max 5MB)", "image": "Image", "name": "Name", "actions": "Actions", "noData": "No nurses found", "reviews": "Reviews", "reviewsFor": "Reviews for", "rating": "Rating", "noReviews": "No reviews available", "validation": {"required": "This field is required", "email": "Please enter a valid email address", "phone": "Please enter a valid phone number", "password": "Password must be at least 8 characters long", "confirmPassword": "Passwords do not match", "license": "Please enter a valid medical license number", "specialization": "Please select a specialization", "governorate": "Please select a governorate", "city": "Please select a city", "image": "Please upload a valid image file"}, "success": {"add": "Nurse added successfully", "edit": "Nurse updated successfully", "delete": "Nurse deleted successfully"}, "error": {"add": "Failed to add nurse", "edit": "Failed to update nurse", "delete": "Failed to delete nurse", "load": "Failed to load nurses"}}, "map": {"instructions": "Click on the map to select a location or drag the marker to adjust position", "showMap": "Show Map", "hideMap": "Hide Map", "latitude": "Latitude", "longitude": "Longitude", "selectLocation": "Select Location", "currentLocation": "Use Current Location", "locationSelected": "Location Selected", "searchLocation": "Search Location"}, "requests": {"title": "Requests Management", "from": "From", "to": "To", "filter": "Filter", "current": "Current", "previous": "Previous", "loading": "Loading...", "total": "Requests", "nurse": "Nurse", "phone": "Phone", "status": "Status", "speciality": "Speciality", "totalPrice": "Total Price", "date": "Date", "details": "Details", "detailsTitle": "Request Details", "latitude": "Latitude", "longitude": "Longitude", "nurseLatitude": "Nurse Latitude", "nurseLongitude": "Nurse Longitude", "noRequests": "No requests found.", "prev": "Prev", "next": "Next", "page": "Page", "locationDetails": "Location Details", "distance": "Distance", "distanceKm": "Distance in Kilometers", "distanceBetween": "Distance between {0} and {1}", "distanceToNurse": "Distance to Nurse", "distanceToUser": "Distance to User", "loadingDetails": "Loading details...", "confirmDelete": "Are you sure you want to delete this request?", "delete": "Delete", "edit": "Edit", "view": "View", "cancel": "Cancel", "confirm": "Confirm", "close": "Close", "requestDetails": "Request Details"}, "REPORTS": {"TITLE": "Medical Reports", "SUBTITLE": "Manage patient medical reports and health records", "FILTERS": {"FROM_DATE": "From Date", "TO_DATE": "To Date"}, "BUTTONS": {"ADD_REPORT": "Add Report", "SEARCH": "Search", "RESET": "Reset", "VIEW_DETAILS": "View Details", "PATIENT_REPORTS": "Patient Reports"}, "TABLE": {"TITLE": "Reports List", "PATIENT_NAME": "Patient Name", "PATIENT_PHONE": "Patient Phone", "NURSE_NAME": "Nurse Name", "CREATED_AT": "Created Date", "DISEASES": "Diseases", "ACTIONS": "Actions"}, "DETAILS": {"TITLE": "Report Details", "SUBTITLE": "Detailed medical report information", "PATIENT_INFO": "Patient Information", "NURSE_INFO": "Nurse Information", "MEDICAL_INFO": "Medical Information", "DATE_INFO": "Date Information", "PATIENT_NAME": "Patient Name", "PATIENT_PHONE": "Patient Phone", "NURSE_NAME": "Nurse Name", "NURSE_PHONE": "Nurse Phone", "DRUGS": "Medications", "DISEASES": "Diseases", "NOTES": "Notes", "CREATED_DATE": "Created Date", "CREATED_TIME": "Created Time"}, "NO_DATA": {"TITLE": "No Reports Found", "MESSAGE": "No medical reports available. Add a new report to get started."}, "PATIENT_REPORTS": {"TITLE": "Patient Medical Reports", "SUBTITLE": "All medical reports for patient", "FOUND_REPORTS": "Found Reports", "ADD_NEW_REPORT": "Add New Report", "NO_REPORTS": {"TITLE": "No Medical Reports Found", "MESSAGE": "No medical reports have been created for patient", "SUGGESTIONS": {"TITLE": "What you can do:", "ITEM1": "Create a new medical report for this patient", "ITEM2": "Check if the patient has reports under a different name", "ITEM3": "Contact the medical team for more information"}, "ADD_REPORT": "Create First Report"}}}, "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "completed": "Completed", "cancelled": "Cancelled", "approved": "Approved", "rejected": "Rejected"}, "messages": {"success": "Operation completed successfully", "error": "An error occurred", "warning": "Warning", "info": "Information", "deleteSuccess": "Item deleted successfully", "updateSuccess": "Item updated successfully", "addSuccess": "Item added successfully", "deleteError": "Error deleting item", "updateError": "Error updating item", "addError": "Error adding item", "noData": "No data available", "noResults": "No results found"}, "PHARMACY": {"TITLE": "Pharmacies", "ADD_NEW": "Add New Pharmacy", "ADD_TITLE": "Add New Pharmacy", "EDIT_TITLE": "Edit Pharmacy", "TABLE": {"NAME": "Name", "ADDRESS": "Address", "PHONE": "Phone", "EMAIL": "Email", "GOVERNORATE": "Governorate", "CITY": "City", "ACTIONS": "Actions"}, "FORM": {"NAME": "Pharmacy Name", "NAME_PLACEHOLDER": "Enter pharmacy name", "NAME_REQUIRED": "Pharmacy name is required", "ADDRESS": "Address", "ADDRESS_PLACEHOLDER": "Enter pharmacy address", "ADDRESS_REQUIRED": "Address is required", "PHONE": "Phone Number", "PHONE_PLACEHOLDER": "Enter phone number", "PHONE_REQUIRED": "Valid phone number is required", "EMAIL": "Email Address", "EMAIL_PLACEHOLDER": "Enter email address", "EMAIL_INVALID": "Please enter a valid email address", "GOVERNORATE": "Governorate", "GOVERNORATE_PLACEHOLDER": "Select Governorate", "CITY": "City", "CITY_PLACEHOLDER": "Select City", "LOCATION_SECTION": "Location Information", "LATITUDE": "Latitude", "LATITUDE_PLACEHOLDER": "Select location on map", "LONGITUDE": "Longitude", "LONGITUDE_PLACEHOLDER": "Select location on map", "NOTES": "Notes", "NOTES_PLACEHOLDER": "Enter additional notes", "ADDRESS_NOTES": "Address Notes", "ADDRESS_NOTES_PLACEHOLDER": "Enter address details", "VALIDATION_ERROR": "Please fill in all required fields correctly"}, "MAP": {"TITLE": "Select Pharmacy Location", "SELECT_LOCATION": "Select Location on Map", "HIDE_MAP": "Hide Map", "USE_CURRENT_LOCATION": "Use Current Location", "INSTRUCTIONS": "Click on the map to set pharmacy location", "DETAILED_INSTRUCTIONS": "Click anywhere on the map to set the pharmacy location. You can also drag the marker to fine-tune the position.", "LOCATION_ERROR": "Unable to get current location. Please select manually on the map.", "GEOLOCATION_NOT_SUPPORTED": "Geolocation is not supported by this browser."}, "BUTTONS": {"EDIT": "Edit", "DELETE": "Delete", "CANCEL": "Cancel", "ADD": "Add Pharmacy", "UPDATE": "Update Pharmacy"}, "DELETE": {"TITLE": "Delete Pharmacy", "MESSAGE": "Are you sure you want to delete this pharmacy?"}, "MESSAGES": {"ADD_SUCCESS": "Pharmacy added successfully", "UPDATE_SUCCESS": "Pharmacy updated successfully", "DELETE_SUCCESS": "Pharmacy deleted successfully", "DELETE_CONFIRM": "Are you sure you want to delete this pharmacy?", "ERROR": {"LOAD_PHARMACIES": "Error loading pharmacies", "LOAD_GOVERNORATES": "Error loading governorates", "LOAD_CITIES": "Error loading cities", "ADD": "Error adding pharmacy", "UPDATE": "Error updating pharmacy", "DELETE": "Error deleting pharmacy"}}}, "RESERVATION": {"TITLE": "Reservations Management", "SUBTITLE": "Manage patient reservations and appointments", "FILTERS": {"TITLE": "Search & Filter", "SEARCH": "Search", "SEARCH_PLACEHOLDER": "Search by patient name or phone", "STATUS": "Status", "ALL_STATUS": "All Status", "FROM_DATE": "From Date", "TO_DATE": "To Date"}, "TABLE": {"TITLE": "Reservations List", "ID": "ID", "PATIENT_NAME": "Patient Name", "PHONE": "Phone", "DATE": "Date", "TIME": "Time", "STATUS": "Status", "LOCATION": "Location", "ACTIONS": "Actions"}, "STATUS": {"NEW": "New", "IN_PROGRESS": "In Progress", "COMPLETED": "Completed", "CANCELLED": "Cancelled"}, "BUTTONS": {"SEARCH": "Search", "RESET": "Reset", "CLEAR": "Clear", "VIEW_DETAILS": "View Details", "COMPLETE": "Complete", "CANCEL": "Cancel", "DELETE": "Delete", "PRINT": "Print"}, "DETAILS": {"TITLE": "Reservation Details", "SUBTITLE": "Detailed information for reservation", "ID": "Reservation ID", "PATIENT_NAME": "Patient Name", "PHONE": "Phone Number", "DATE": "Appointment Date", "TIME": "Appointment Time", "STATUS": "Status", "GOVERNORATE": "Governorate", "CITY": "City", "ADDRESS_NOTES": "Address Notes", "NOTES": "Notes", "PATIENT_INFO": "Patient Information", "APPOINTMENT_INFO": "Appointment Information", "LOCATION_INFO": "Location Information", "ADDITIONAL_INFO": "Additional Information"}, "CONFIRM": {"TITLE": "Confirm Action", "COMPLETE_TITLE": "Complete Reservation", "COMPLETE_MESSAGE": "Are you sure you want to mark this reservation as completed?", "CANCEL_TITLE": "Cancel Reservation", "CANCEL_MESSAGE": "Are you sure you want to cancel this reservation?", "DELETE_TITLE": "Delete Reservation", "DELETE_MESSAGE": "Are you sure you want to delete this reservation? This action cannot be undone."}, "MESSAGES": {"COMPLETED_SUCCESS": "Reservation completed successfully", "CANCELLED_SUCCESS": "Reservation cancelled successfully", "DELETED_SUCCESS": "Reservation deleted successfully", "LOAD_ERROR": "Error loading reservations", "ACTION_ERROR": "Error performing action"}, "NO_DATA": {"TITLE": "No Reservations Found", "MESSAGE": "No reservations match your current filters. Try adjusting your search criteria."}}, "landing": {"features": "Features", "how_it_works": "How It Works", "why_choose": "Why proCare", "faq": "FAQ", "hero_title": "Healthcare at Your Doorstep", "hero_subtitle": "Book a nurse, order medicine, all from the comfort of your home", "features_title": "proCare Features", "feature1_title": "Qualified Nurses", "feature1_desc": "A team of certified and trained nurses to provide the best healthcare at your home", "feature2_title": "Medicine Delivery", "feature2_desc": "Get your medicines from the nearest pharmacy with fast and secure delivery", "feature3_title": "Medical Records", "feature3_desc": "Keep all your medical records organized and accessible anytime", "feature4_title": "Easy Booking", "feature4_desc": "Book your appointment with one click and choose your preferred time", "how_it_works_title": "How proCare Works", "step1_title": "Download the App", "step1_desc": "Download the proCare app from the app store and create your account", "step2_title": "Book a Service", "step2_desc": "Choose the service type and book your preferred time", "step3_title": "Receive Service", "step3_desc": "Receive the nurse or medicine at your home at the scheduled time", "why_choose_title": "Why Choose proCare", "benefit1_title": "Certified Staff", "benefit1_desc": "All nurses are certified and licensed by the Ministry of Health", "benefit2_title": "Security & Privacy", "benefit2_desc": "We maintain your medical data privacy with the highest security standards", "benefit3_title": "Secure Payment", "benefit3_desc": "Multiple secure payment methods including credit cards and cash on delivery", "benefit4_title": "Flexible Hours", "benefit4_desc": "Service available 24/7 with options for advance or emergency booking", "testimonials_title": "What Our Clients Say", "testimonial1_text": "Excellent and fast service. The nurse was very professional and took great care of my mother. I recommend this app to everyone.", "testimonial1_author": "<PERSON>'s Mother", "testimonial1_location": "Riyadh", "testimonial2_text": "Medicine delivery was very fast and arrived the same day. The app is easy to use and prices are reasonable.", "testimonial2_author": "<PERSON>", "testimonial2_location": "Jeddah", "testimonial3_text": "Best home healthcare app. The staff is well-trained and qualified, and service is available anytime.", "testimonial3_author": "Fatima Al-Salem", "testimonial3_location": "<PERSON><PERSON><PERSON>", "faq_title": "Frequently Asked Questions", "faq1_question": "How can I ensure the quality of medical staff?", "faq1_answer": "All nurses at proCare are certified by the Ministry of Health and have a minimum of 3 years of medical experience. We also conduct regular checks and continuous training to ensure the highest level of service.", "faq2_question": "How long does it take for the nurse or medicine to arrive?", "faq2_answer": "We strive to provide fast service where nurses arrive within 30-60 minutes for regular cases, and within 15-30 minutes for emergencies. For medicines, delivery takes 2-4 hours depending on the area.", "faq3_question": "What payment methods are available?", "faq3_answer": "We accept all major payment methods: credit cards (Visa, Mastercard), Mada, Apple Pay, and cash on delivery. All transactions are secure and encrypted.", "faq4_question": "Can I order medicines without a prescription?", "faq4_answer": "Yes, you can order over-the-counter medicines. For prescription medicines, you need to upload a copy of the prescription from a licensed doctor.", "faq5_question": "Is the service available 24/7?", "faq5_answer": "Yes, proCare service is available 24/7 throughout the week. You can book in advance or request emergency service at any time.", "cta_title": "Start Your Journey with proCare Today", "cta_subtitle": "Download the app now and get the best healthcare at your home", "cta_feature1": "Free Download", "cta_feature2": "24/7 Service", "cta_feature3": "Certified Staff", "footer_tagline": "Healthcare at Your Doorstep", "footer_description": "proCare app provides the best home healthcare services with high quality and exceptional professionalism", "footer_quick_links": "Quick Links", "footer_contact": "Contact Us", "footer_phone": "+966 11 123 4567", "footer_email": "<EMAIL>", "footer_address": "Riyadh, Kingdom of Saudi Arabia", "footer_download": "Download App", "footer_rights": "All rights reserved", "services_title": "Services", "service1": "Home Nursing", "service2": "Medicine Delivery", "service3": "Medical Consultations", "service4": "Home Tests", "support_title": "Support", "support1": "Help Center", "support2": "Contact Us", "support3": "FAQ", "support4": "Privacy Policy", "contact_title": "Contact Us", "contact_address": "Riyadh, Kingdom of Saudi Arabia", "copyright": "© 2024 proCare. All rights reserved."}, "appVersion": {"title": "App Version", "subtitle": "Manage mobile application versions and force updates", "filterSubtitle": "Search and filter app versions by criteria", "activeFilters": "Active Filters", "addVersion": "Add Version", "editVersion": "Edit Version", "deleteVersion": "Delete Version", "forceUpdate": "Force Update", "versionsList": "Versions List", "version": "Version", "platform": "Platform", "versionPlaceholder": "e.g., 1.0.0", "versionFormat": "Format: major.minor.patch (e.g., 1.0.0)", "selectPlatform": "Select Platform", "selectPlatformFirst": "Select platform first", "selectVersion": "Select Version", "selectPlatformToSeeVersions": "Please select a platform to see available versions", "availableVersionsFound": "Available versions found", "noVersionsAvailable": "No versions available", "noVersionsForPlatform": "No versions found for this platform", "latest": "Latest", "choosePlatformHelp": "Select the platform for this app version", "confirmForceUpdate": "Confirm Force Update", "criticalAction": "Critical Action Required", "forceUpdateCriticalWarning": "This action will immediately force ALL users on the selected platform to update their app. This cannot be undone.", "forceUpdateDetails": "Force Update Details", "targetVersion": "Target Version", "whatWillHappen": "What will happen:", "impactPoint1": "All users on the selected platform will be forced to update immediately", "impactPoint2": "Users with older versions will not be able to use the app", "impactPoint3": "This action takes effect immediately and cannot be reversed", "impactPoint4": "App store approval may be required for the update to be available", "confirmUnderstanding": "I understand the consequences and want to proceed with the force update", "executeForceUpdate": "Execute Force Update", "forceUpdateWarning": "This will force all users on the selected platform to update to this version.", "deleteConfirmation": "Are you sure you want to delete this version?", "noDataFound": "No versions found", "messages": {"addSuccess": "Version added successfully", "updateSuccess": "Version updated successfully", "deleteSuccess": "Version deleted successfully", "forceUpdateSuccess": "Force update initiated successfully"}, "errors": {"loadFailed": "Failed to load versions", "addFailed": "Failed to add version", "updateFailed": "Failed to update version", "deleteFailed": "Failed to delete version", "forceUpdateFailed": "Failed to initiate force update"}, "validation": {"versionPattern": "Please enter a valid version format (e.g., 1.0.0)"}, "platforms": {"ios": "iOS", "android": "Android", "web": "Web"}}}